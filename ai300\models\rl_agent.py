import gym
import numpy as np
from stable_baselines3 import PPO
import os

def train_rl_agent(env_id='CartPole-v1', timesteps=10000):
    env = gym.make(env_id)
    model = PPO('MlpPolicy', env, verbose=1)
    model.learn(total_timesteps=timesteps)
    ASSETS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib', 'assets', 'models')
    os.makedirs(ASSETS_DIR, exist_ok=True)
    model.save(os.path.join(ASSETS_DIR, 'rl_model.zip'))
    print(f'RL model exported to {os.path.join(ASSETS_DIR, "rl_model.zip")}')

if __name__ == '__main__':
    train_rl_agent() 