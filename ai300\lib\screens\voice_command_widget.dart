import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';

class VoiceCommandWidget extends StatefulWidget {
  final Function(String command) onCommand;
  const VoiceCommandWidget({Key? key, required this.onCommand}) : super(key: key);

  @override
  State<VoiceCommandWidget> createState() => _VoiceCommandWidgetState();
}

class _VoiceCommandWidgetState extends State<VoiceCommandWidget> {
  late stt.SpeechToText _speech;
  bool _isListening = false;
  String _lastWords = '';
  final FlutterTts _tts = FlutterTts();

  @override
  void initState() {
    super.initState();
    _speech = stt.SpeechToText();
  }

  Future<void> _listen() async {
    if (!_isListening) {
      bool available = await _speech.initialize();
      if (available) {
        setState(() => _isListening = true);
        _speech.listen(
          onResult: (val) {
            setState(() => _lastWords = val.recognizedWords);
            if (val.finalResult) {
              _handleCommand(_lastWords);
              setState(() => _isListening = false);
              _speech.stop();
            }
          },
          localeId: 'ar_SA', // يدعم العربية والإنجليزية
        );
      }
    } else {
      setState(() => _isListening = false);
      _speech.stop();
    }
  }

  Future<void> _handleCommand(String command) async {
    String response = '';
    if (command.contains('تنفيذ صفقة') || command.toLowerCase().contains('trade')) {
      widget.onCommand('trade');
      response = 'تم تنفيذ الصفقة.';
    } else if (command.contains('الشارت') || command.toLowerCase().contains('chart')) {
      widget.onCommand('charts');
      response = 'تم عرض الشارت.';
    } else if (command.contains('لوحة التحكم') || command.toLowerCase().contains('dashboard')) {
      widget.onCommand('dashboard');
      response = 'تم عرض لوحة التحكم.';
    } else {
      response = 'عذراً، لم أفهم الأمر.';
    }
    await _tts.speak(response);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ElevatedButton.icon(
          icon: Icon(_isListening ? Icons.mic : Icons.mic_none),
          label: Text(_isListening ? 'استمع...' : 'تحكم صوتي'),
          onPressed: _listen,
        ),
        if (_lastWords.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text('آخر أمر: $_lastWords'),
          ),
      ],
    );
  }
} 