import 'dart:math';
import 'package:tflite_flutter/tflite_flutter.dart';
import '../data/models/candle_data.dart';

class MLModels {
  static Future<Interpreter> _loadModel(String modelPath) async {
    return await Interpreter.fromAsset(modelPath);
  }
  
  static List<double> normalizeFeatures(List<double> features, List<double> means, List<double> stds) {
    List<double> normalized = [];
    for (int i = 0; i < features.length; i++) {
      normalized.add((features[i] - means[i]) / stds[i]);
    }
    return normalized;
  }
  
  static Future<double> predictLinearRegression(List<double> features) async {
    final interpreter = await _loadModel('assets/models/linear_regression.tflite');
    
    // Normalize features (assuming means and stds are stored in the model)
    List<double> means = [0, 0, 0, 0]; // Replace with actual means
    List<double> stds = [1, 1, 1, 1];  // Replace with actual stds
    List<double> normalizedFeatures = normalizeFeatures(features, means, stds);
    
    // Prepare input and output tensors
    var input = [normalizedFeatures];
    var output = List<double>.filled(1, 0).reshape([1, 1]);
    
    // Run inference
    interpreter.run(input, output);
    
    // Close the interpreter
    interpreter.close();
    
    return output[0][0];
  }
  
  static Future<List<double>> predictProbabilities(List<double> features) async {
    final interpreter = await _loadModel('assets/models/random_forest.tflite');
    
    // Normalize features
    List<double> means = [0, 0, 0, 0]; // Replace with actual means
    List<double> stds = [1, 1, 1, 1];  // Replace with actual stds
    List<double> normalizedFeatures = normalizeFeatures(features, means, stds);
    
    // Prepare input and output tensors
    var input = [normalizedFeatures];
    var output = List<double>.filled(3, 0).reshape([1, 3]); // Assuming 3 classes: up, down, sideways
    
    // Run inference
    interpreter.run(input, output);
    
    // Close the interpreter
    interpreter.close();
    
    return output[0];
  }
  
  static List<double> extractFeatures(List<CandleData> candles, int index) {
    // Extract relevant features for prediction
    // This is a simplified example - real feature extraction would be more complex
    
    if (index < 5 || index >= candles.length) {
      throw Exception('Invalid index for feature extraction');
    }
    
    // Calculate price changes
    List<double> priceChanges = [];
    for (int i = 1; i <= 5; i++) {
      priceChanges.add(candles[index - i + 1].close / candles[index - i].close - 1);
    }
    
    // Calculate volume changes
    List<double> volumeChanges = [];
    for (int i = 1; i <= 5; i++) {
      volumeChanges.add(candles[index - i + 1].volume / candles[index - i].volume - 1);
    }
    
    // Calculate volatility (using high-low range)
    List<double> volatility = [];
    for (int i = 0; i < 5; i++) {
      volatility.add((candles[index - i].high - candles[index - i].low) / candles[index - i].close);
    }
    
    // Combine all features
    List<double> features = [