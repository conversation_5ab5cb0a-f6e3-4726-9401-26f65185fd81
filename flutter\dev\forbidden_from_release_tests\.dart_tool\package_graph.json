{"roots": ["forbidden_from_release_tests"], "packages": [{"name": "forbidden_from_release_tests", "version": "0.0.0", "dependencies": ["args", "collection", "file", "meta", "package_config", "path", "platform", "process", "vm_snapshot_analysis"], "devDependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "vm_snapshot_analysis", "version": "0.7.6", "dependencies": ["args", "collection", "path"]}, {"name": "process", "version": "5.0.3", "dependencies": ["file", "path", "platform"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "args", "version": "2.7.0", "dependencies": []}], "configVersion": 1}