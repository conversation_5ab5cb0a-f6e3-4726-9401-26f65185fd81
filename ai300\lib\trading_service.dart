import 'dart:convert';
import 'dart:io';

class TradingService {
  static Future<Map<String, dynamic>> placeOrder({
    required String symbol,
    required String side, // 'buy' or 'sell'
    required double amount,
    bool testnet = true,
  }) async {
    try {
      final process = await Process.start(
        'python',
        ['models/trading_bridge.py', 'order', symbol, side, amount.toString(), testnet.toString()],
        runInShell: true,
      );
      final output = await process.stdout.transform(utf8.decoder).join();
      return jsonDecode(output);
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> getBalance({bool testnet = true}) async {
    try {
      final process = await Process.start(
        'python',
        ['models/trading_bridge.py', 'balance', testnet.toString()],
        runInShell: true,
      );
      final output = await process.stdout.transform(utf8.decoder).join();
      return jsonDecode(output);
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> placeLimitOrder({
    required String symbol,
    required String side,
    required double amount,
    required double price,
    bool testnet = true,
  }) async {
    try {
      final process = await Process.start(
        'python',
        ['models/trading_bridge.py', 'limit', symbol, side, amount.toString(), price.toString(), testnet.toString()],
        runInShell: true,
      );
      final output = await process.stdout.transform(utf8.decoder).join();
      return jsonDecode(output);
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> placeStopOrder({
    required String symbol,
    required String side,
    required double amount,
    required double stopPrice,
    bool testnet = true,
  }) async {
    try {
      final process = await Process.start(
        'python',
        ['models/trading_bridge.py', 'stop', symbol, side, amount.toString(), stopPrice.toString(), testnet.toString()],
        runInShell: true,
      );
      final output = await process.stdout.transform(utf8.decoder).join();
      return jsonDecode(output);
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> getOrderHistory({String? symbol, bool testnet = true}) async {
    try {
      final args = [
        'models/trading_bridge.py',
        'orders',
        if (symbol != null) symbol,
        testnet.toString(),
      ];
      final process = await Process.start('python', args, runInShell: true);
      final output = await process.stdout.transform(utf8.decoder).join();
      return jsonDecode(output);
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  static Future<Map<String, dynamic>> executeArbitrage({
    required String symbol,
    required double amount,
    double minProfit = 1,
    bool testnet = true,
  }) async {
    try {
      final process = await Process.start(
        'python',
        [
          'models/arbitrage_engine.py',
          '--symbol', symbol,
          '--amount', amount.toString(),
          '--min_profit', minProfit.toString(),
          if (testnet) '--testnet',
        ],
        runInShell: true,
      );
      final output = await process.stdout.transform(utf8.decoder).join();
      return jsonDecode(output);
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  static Stream<Map<String, dynamic>> monitorArbitrage({
    required String symbol,
    required double amount,
    double minProfit = 1,
    double maxExposure = 0.05,
    bool testnet = true,
    int interval = 10,
  }) async* {
    while (true) {
      final process = await Process.start(
        'python',
        [
          'models/arbitrage_engine.py',
          '--symbol', symbol,
          '--amount', amount.toString(),
          '--min_profit', minProfit.toString(),
          '--max_exposure', maxExposure.toString(),
          '--interval', interval.toString(),
          if (testnet) '--testnet',
        ],
        runInShell: true,
      );
      await for (final line in process.stdout.transform(utf8.decoder).transform(const LineSplitter())) {
        try {
          yield jsonDecode(line);
        } catch (_) {}
      }
      await Future.delayed(Duration(seconds: interval));
    }
  }
} 