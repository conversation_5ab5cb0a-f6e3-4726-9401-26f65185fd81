import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ai_300/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Performance test - Dashboard load', (WidgetTester tester) async {
    final stopwatch = Stopwatch()..start();
    app.main();
    await tester.pumpAndSettle();
    stopwatch.stop();
    print('Dashboard loaded in: ${stopwatch.elapsedMilliseconds} ms');
    expect(stopwatch.elapsedMilliseconds < 2000, true, reason: 'Dashboard should load in <2s');
  });

  testWidgets('Performance test - Charts screen', (WidgetTester tester) async {
    app.main();
    await tester.pumpAndSettle();
    await tester.tap(find.text('الشارتات'));
    await tester.pumpAndSettle();
    // قياس زمن تحميل الشارت
    final stopwatch = Stopwatch()..start();
    await tester.pumpAndSettle();
    stopwatch.stop();
    print('Charts loaded in: ${stopwatch.elapsedMilliseconds} ms');
    expect(stopwatch.elapsedMilliseconds < 2500, true, reason: 'Charts should load in <2.5s');
  });
} 