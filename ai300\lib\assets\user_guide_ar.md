# دليل المستخدم - Signal Black Super Trading

## مقدمة

تطبيق Signal Black Super Trading هو منصة تداول ذكية متكاملة تدعم التحليل الفني، الذكاء الاصطناعي، التداول الآلي، الواقع المعزز، التحكم الصوتي، الترجمة، إدارة المخاطر، الأمان، ودعم المستخدم التفاعلي.

---

## المميزات الرئيسية

### 1. الشاشات الرئيسية
- **لوحة التحكم**: نظرة شاملة على الحساب، الأصول، التنبيهات.
  - ![صورة لوحة التحكم](assets/screens/dashboard.png)
  - [فيديو قصير](assets/videos/dashboard.mp4)
- **الشارتات والتحليل الفني**: عرض الشموع والمؤشرات الفنية والتوصيات.
  - ![صورة الشارت](assets/screens/charts.png)
  - [فيديو قصير](assets/videos/charts.mp4)
- **التحليل الأساسي والاقتصادي**: بيانات Yahoo Finance, Alpha Vantage, Economic Calendar.
  - ![صورة التحليل الأساسي](assets/screens/analysis.png)
- **التداول**: تنفيذ أوامر Market/Limit/Stop مع إدارة المخاطر.
  - ![صورة التداول](assets/screens/trading.png)
- **المحفظة**: تقارير أداء، مخاطر، Monte Carlo.
  - ![صورة المحفظة](assets/screens/portfolio.png)
- **الإعدادات**: تخصيص الألوان، اللغة، الأمان.
- **المساعدة**: دليل تفاعلي، أسئلة شائعة.

### 2. الذكاء الاصطناعي والتوصيات
- **نماذج هجينة (LSTM, CNN, Transformer)**: توصيات دقيقة.
- **XAI/SHAP**: شرح سبب التوصية للمستخدم.
- **AutoML/River**: تعلم مستمر وتطوير ذاتي للنماذج.

### 3. التداول الآلي وإدارة المخاطر
- **ربط Binance, MT5, IB**: تنفيذ أوامر تلقائية.
- **إدارة المخاطر**: Sharpe, Sortino, Calmar, Kelly, Fixed Fractional.
- **محاكاة Monte Carlo**: توقع احتمالات الربح والخسارة.

### 4. الأمان والموثوقية
- **MFA/OTP**: تحقق ثنائي.
- **توقيع رقمي للصفقات**: Hyperledger Fabric.
- **حماية DDoS/IsolationForest**: كشف الأنشطة الشاذة.
- **Logging & Monitoring**: Sentry, Datadog.

### 5. التكاملات والاختبارات
- **اختبارات أداء وتغطية**: Flutter DevTools, CI/CD, GitHub Actions.
- **اختبارات رجعية**: دقة النماذج > 93%.

---

## صور وفيديوهات لكل ميزة
- جميع الصور والفيديوهات مرفقة في مجلد `assets/screens` و`assets/videos`.

---

## تحديث الدليل
- يتم تحديث هذا الدليل تلقائيًا مع كل تطوير جديد.
- لمزيد من الدعم أو الأسئلة: راجع قسم المساعدة داخل التطبيق.

## مصادر البيانات الأساسية والاقتصادية

يعتمد البرنامج على مصادر بيانات عالمية موثوقة لجلب أحدث المعلومات المالية والاقتصادية:

- **Yahoo Finance**: لجلب بيانات الأسعار، الأرباح، العائد السنوي، المؤشرات المالية الأساسية.
- **Alpha Vantage**: لجلب بيانات المؤشرات المالية، رأس المال السوقي، نسبة الربحية، العائد على السهم، وغيرها.
- **Economic Calendar API**: لجلب الأحداث الاقتصادية الهامة مثل بيانات الفائدة، التضخم، الناتج المحلي، البطالة، المؤشرات الاقتصادية العالمية.

يتم عرض مصدر كل معلومة أسفل البيانات في شاشة التحليل الأساسي والاقتصادي.

> **ملاحظة:** جميع البيانات يتم جلبها بشكل مباشر من الإنترنت وتحديثها تلقائيًا عند فتح شاشة التحليل أو عند الضغط على زر التحديث. 

## أمثلة الاختبار الرجعي (Backtest)

- يمكنك إدخال كود الاستراتيجية بلغة بايثون (Zipline) ورفع بيانات الأسعار (CSV) من خلال شاشة "الاختبار الرجعي".
- بعد الضغط على زر "تشغيل الاختبار الرجعي" ستظهر النتائج بشكل جدولي.
- مثال كود بسيط:

```
def initialize(context):
    pass

def handle_data(context, data):
    pass
```

- مثال بيانات أسعار:
```
date,open,high,low,close,volume
2023-01-01,100,110,90,105,1000
2023-01-02,105,115,95,110,1200
...
```

## إدارة النماذج الذكية (AI Models)

### مسار النماذج:
- جميع النماذج المدربة (مثل LSTM, CNN) يجب وضعها في المسار: `lib/assets/models/`
- أمثلة الملفات: `lstm_model.tflite`, `cnn_model.tflite`

### إضافة أو تحديث نموذج:
1. ضع ملف النموذج في `lib/assets/models/`
2. أضف اسمه في قسم `assets` داخل `pubspec.yaml`:
   ```yaml
   assets:
     - assets/models/lstm_model.tflite
     - assets/models/cnn_model.tflite
   ```
3. شغّل الأمر `flutter pub get` بعد أي تحديث.

### اختبار النماذج من الواجهة:
- انتقل إلى شاشة التحليل.
- اضغط زر "اختبار تحميل النماذج والتنبؤ".
- ستظهر نتيجة التنبؤ من كل نموذج (LSTM, CNN) مباشرة.

### ملاحظات تقنية:
- يمكن إضافة دعم لنماذج PyTorch Mobile (`.pt`) أو REST لنماذج متقدمة لاحقًا.
- لتحديث نموذج: استبدل الملف فقط وأعد تشغيل التطبيق.
- جميع الأكواد تدعم التحميل الديناميكي للنماذج من المسار أعلاه.

--- 