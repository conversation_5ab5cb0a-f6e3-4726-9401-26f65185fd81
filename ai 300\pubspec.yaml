name: signal_black_super_trading
version: 0.1.0
publish_to: none
description: >
  تطبيق تداول ذكي متكامل مع تحليلات متقدمة وذكاء اصطناعي.
environment:
  sdk: ">=2.17.0 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  fl_chart: ^0.63.0
  flutter_localizations:
    sdk: flutter
  flutter_secure_storage: ^9.0.0
  flutter_local_notifications: ^16.1.0
  wear: ^1.0.0
  http: ^1.2.1
  intl: ^0.19.0
  sqflite: ^2.3.2
  path: ^1.9.0
  google_fonts: ^6.1.0
  pdf: ^3.10.4
  image_picker: ^1.0.7
  flutter_svg: ^2.0.10+1
  arcore_flutter_plugin: ^0.0.17
  speech_to_text: ^6.6.0
  flutter_tts: ^4.0.2
  provider: ^6.1.2
  path_provider: ^2.0.15
  flutter_colorpicker: ^1.0.3
  shared_preferences: ^2.2.2
  ar_flutter_plugin: ^0.7.3
  share_plus: ^7.2.1
  # tflite_flutter: ^0.10.3  # Commented out for now
  # pytorch_mobile: ^1.0.2   # Commented out for now
  # sentry_flutter: ^7.0.0   # Commented out for now
  # datadog_flutter_plugin: ^1.2.0  # Commented out for now
  reorderables: ^0.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/
    - assets/models/
    - assets/images/

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic