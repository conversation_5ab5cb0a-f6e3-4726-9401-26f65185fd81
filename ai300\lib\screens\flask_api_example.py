from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

strategies = []

@app.route('/strategies', methods=['GET', 'POST'])
def handle_strategies():
    if request.method == 'POST':
        data = request.json
        data['votes'] = 0
        strategies.append(data)
        return jsonify({'status': 'added'}), 201
    return jsonify(strategies)

@app.route('/strategies/<int:index>/vote', methods=['POST'])
def vote_strategy(index):
    if 0 <= index < len(strategies):
        strategies[index]['votes'] += 1
        return jsonify({'status': 'voted', 'votes': strategies[index]['votes']})
    return jsonify({'error': 'not found'}), 404

if __name__ == '__main__':
    app.run(debug=True) 