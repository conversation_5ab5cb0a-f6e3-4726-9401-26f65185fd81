import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../main.dart';
import '../analysis.dart';
import '../ai_models.dart';
import 'dart:math';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import '../ai_bridge.dart';
import 'package:share_plus/share_plus.dart';
import 'voice_command.dart';
import '../wear_service.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:csv/csv.dart';
import '../gann_bridge.dart';
import '../xai_bridge.dart';

class ChartsScreen extends StatefulWidget {
  final String? aiRecommendation;
  final double? aiConfidence;
  final Map<String, double>? shapFeatureImportances;
  const ChartsScreen({super.key, this.aiRecommendation, this.aiConfidence, this.shapFeatureImportances});

  @override
  State<ChartsScreen> createState() => _ChartsScreenState();
}

class _ChartsScreenState extends State<ChartsScreen> {
  List<CandleData> candles = [];
  bool loading = true;
  String? error;
  Map<String, dynamic>? yahooData;
  double? lstmPrediction;
  double? lstmConfidence;
  bool? isHeadAndShoulders;
  bool? isWedge;
  double? monteCarloFinal;
  double? backtestResult;
  String? xaiExplanation;
  String? detectedPattern;
  String? detectedPatternML;
  String? recommendationReport;
  String? shortTermRecommendation;
  double? shortTermConfidence;
  String? longTermRecommendation;
  double? longTermConfidence;
  String? shortTermReport;
  String? longTermReport;
  // تخصيص الألوان والمؤشرات
  Color closeColor = Colors.blue;
  Color smaColor = Colors.orange;
  Color emaColor = Colors.green;
  Color rsiColor = Colors.purple;
  Color macdColor = Colors.red;
  Color bollColor = Colors.teal;
  bool showSMA = true;
  bool showEMA = true;
  bool showRSI = true;
  bool showMACD = true;
  bool showBoll = true;
  double? entryPrice;
  double? stopLoss;
  double? takeProfit;
  bool showXaiDialog = false;
  Map<String, dynamic>? taIndicators;
  bool showStochastic = false;
  bool showATR = false;
  bool showOBV = false;
  bool showWilliamsR = false;
  bool showVPT = false;
  bool showMomentum = false;
  bool showDemark = false;
  bool showAD = false;
  bool showIchimoku = false;
  bool showFibonacci = false;
  bool showParabolicSAR = false;
  final ScreenshotController screenshotController = ScreenshotController();
  List<String> indicatorOrder = [
    'SMA', 'EMA', 'RSI', 'MACD', 'Bollinger', 'Stochastic', 'ATR', 'OBV', 'WilliamsR', 'VPT', 'Momentum', 'Demark', 'AD', 'Ichimoku', 'Fibonacci', 'ParabolicSAR'
  ];
  Map<String, bool> indicatorVisibility = {};
  Map<String, Color> indicatorColors = {
    'SMA': Colors.orange,
    'EMA': Colors.green,
    'RSI': Colors.purple,
    'MACD': Colors.red,
    'Bollinger': Colors.teal,
    'Stochastic': Colors.purple,
    'ATR': Colors.brown,
    'OBV': Colors.indigo,
    'WilliamsR': Colors.deepOrange,
    'VPT': Colors.teal,
    'Momentum': Colors.pink,
    'Demark': Colors.lime,
    'AD': Colors.cyan,
    'Ichimoku': Colors.deepPurple,
    'Fibonacci': Colors.amber,
    'ParabolicSAR': Colors.black,
  };
  final List<String>? highlightFeatures;
  final GlobalKey _chartKey = GlobalKey();
  Map<String, dynamic>? gannResults;
  List<double>? shapImportances;

  @override
  void initState() {
    super.initState();
    fetchCandles();
    fetchYahoo();
    loadIndicatorPrefs();
    loadIndicatorColorPrefs();
  }

  Future<void> loadIndicatorPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      indicatorOrder = prefs.getStringList('indicatorOrder') ?? indicatorOrder;
      indicatorVisibility = {
        'SMA': prefs.getBool('showSMA') ?? showSMA,
        'EMA': prefs.getBool('showEMA') ?? showEMA,
        'RSI': prefs.getBool('showRSI') ?? showRSI,
        'MACD': prefs.getBool('showMACD') ?? showMACD,
        'Bollinger': prefs.getBool('showBoll') ?? showBoll,
        'Stochastic': prefs.getBool('showStochastic') ?? showStochastic,
        'ATR': prefs.getBool('showATR') ?? showATR,
        'OBV': prefs.getBool('showOBV') ?? showOBV,
        'WilliamsR': prefs.getBool('showWilliamsR') ?? showWilliamsR,
        'VPT': prefs.getBool('showVPT') ?? showVPT,
        'Momentum': prefs.getBool('showMomentum') ?? showMomentum,
        'Demark': prefs.getBool('showDemark') ?? showDemark,
        'AD': prefs.getBool('showAD') ?? showAD,
        'Ichimoku': prefs.getBool('showIchimoku') ?? showIchimoku,
        'Fibonacci': prefs.getBool('showFibonacci') ?? showFibonacci,
        'ParabolicSAR': prefs.getBool('showParabolicSAR') ?? showParabolicSAR,
      };
    });
  }

  Future<void> saveIndicatorPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('indicatorOrder', indicatorOrder);
    for (final entry in indicatorVisibility.entries) {
      await prefs.setBool('show${entry.key}', entry.value);
    }
  }

  Future<void> loadIndicatorColorPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      for (final key in indicatorColors.keys) {
        final colorValue = prefs.getInt('color_$key');
        if (colorValue != null) {
          indicatorColors[key] = Color(colorValue);
        }
      }
    });
  }

  Future<void> saveIndicatorColorPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    for (final entry in indicatorColors.entries) {
      await prefs.setInt('color_${entry.key}', entry.value.value);
    }
  }

  Future<void> saveChartAsImage() async {
    final image = await screenshotController.capture();
    if (image == null) return;
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/chart_${DateTime.now().millisecondsSinceEpoch}.png');
    await file.writeAsBytes(image);
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تم حفظ صورة الشارت: ${file.path}')));
  }

  Future<void> fetchCandles() async {
    setState(() {
      loading = true;
      error = null;
    });
    try {
      // جلب بيانات الشموع اليابانية من Binance (آخر 50 شمعة)
      final url = 'https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=50';
      final response = await fetchHttp(url);
      candles = (response as List).map((e) => CandleData.fromList(e)).toList();
      // توصية الذكاء الاصطناعي البسيط
      if (candles.length > 7) {
        final closes = candles.map((c) => c.close).toList();
        lstmPrediction = await AIBridge.predictLSTM(closes);
        // حساب نسبة الثقة بناءً على الانحراف المعياري
        double mean = closes.reduce((a, b) => a + b) / closes.length;
        double std = closes.length > 1 ? sqrt(closes.map((c) => pow(c - mean, 2)).reduce((a, b) => a + b) / closes.length) : 0;
        lstmConfidence = mean != 0 ? (1 - (std / mean)).clamp(0, 1) : null;
        // اكتشاف النمط الفني عبر بايثون
        detectedPattern = await AIBridge.detectPattern(closes);
        detectedPatternML = await AIBridge.detectPatternML(closes);
        // تحليل الأنماط الفنية
        isHeadAndShoulders = PatternRecognition.detectHeadAndShoulders(closes);
        isWedge = PatternRecognition.detectWedge(closes);
        // Monte Carlo
        final monte = QuantitativeAnalysis.monteCarlo(closes.last, 20, 0.001, 0.01);
        monteCarloFinal = monte.last;
        // Backtesting
        backtestResult = QuantitativeAnalysis.backtest(closes);
        // شرح XAI مبسط (تأثير آخر 3 أسعار)
        if (closes.length > 3) {
          final diffs = [closes[closes.length-1]-closes[closes.length-2], closes[closes.length-2]-closes[closes.length-3]];
          xaiExplanation = 'آخر تغيرين: ${diffs.map((d) => d.toStringAsFixed(2)).join(', ')}';
        }
        // تحليل Gann والدورات الزمنية
        gannResults = await GannBridge.analyzeGannCycles(closes);
        // شرح SHAP
        shapImportances = await XAIBridge.getShapImportances(closes);
      }
      if (candles.length > 10) {
        entryPrice = candles.last.close;
        stopLoss = candles.sublist(candles.length-10).map((c) => c.low).reduce(min);
        takeProfit = candles.sublist(candles.length-10).map((c) => c.high).reduce(max);
      }
      // توليد تقرير نصي مفصل للتوصية
      if (candles.isNotEmpty) {
        final closes = candles.map((c) => c.close).toList();
        List<String> reasons = [];
        if (showSMA && closes.length >= 7) {
          double sma = closes.sublist(closes.length-7).reduce((a,b)=>a+b)/7;
          if ((closes.last - sma).abs()/sma > 0.01) reasons.add('تقاطع مع المتوسط المتحرك البسيط (SMA)');
        }
        if (showEMA && closes.length >= 14) {
          double ema = closes.last;
          double k = 2 / (14 + 1);
          for (int i = closes.length-14; i < closes.length; i++) {
            ema = closes[i] * k + ema * (1 - k);
          }
          if ((closes.last - ema).abs()/ema > 0.01) reasons.add('تقاطع مع المتوسط المتحرك الأسي (EMA)');
        }
        if (showRSI && closes.length >= 15) {
          double gain = 0, loss = 0;
          for (int i = closes.length-14; i < closes.length; i++) {
            double change = closes[i] - closes[i-1];
            if (change > 0) gain += change; else loss -= change;
          }
          double rs = loss == 0 ? 100 : gain / loss;
          double rsi = 100 - (100 / (1 + rs));
          if (rsi > 70) reasons.add('RSI مرتفع (تشبع شراء)');
          if (rsi < 30) reasons.add('RSI منخفض (تشبع بيع)');
        }
        if (showMACD && closes.length >= 26) {
          // مبسط: فرق EMA12 وEMA26
          double ema12 = closes.last, ema26 = closes.last;
          double k12 = 2 / (12 + 1), k26 = 2 / (26 + 1);
          for (int i = closes.length-12; i < closes.length; i++) ema12 = closes[i] * k12 + ema12 * (1 - k12);
          for (int i = closes.length-26; i < closes.length; i++) ema26 = closes[i] * k26 + ema26 * (1 - k26);
          if ((ema12 - ema26).abs()/ema26 > 0.01) reasons.add('إشارة MACD');
        }
        if (detectedPattern != null && detectedPattern != 'None') reasons.add('تم اكتشاف نمط فني: $detectedPattern');
        String conf = lstmConfidence != null ? 'نسبة الثقة: ${(lstmConfidence!*100).toStringAsFixed(1)}%' : '';
        String fa = yahooData != null ? 'ملخص التحليل الأساسي: السعر الحالي: ${yahooData!['regularMarketPrice']}, التغير اليومي: ${yahooData!['regularMarketChangePercent']?.toStringAsFixed(2)}%' : '';
        recommendationReport = 'تقرير التوصية:\n${reasons.map((e)=>'- $e').join('\n')}\n$conf\n$fa';
      }
      // توصية قصيرة الأجل (آخر 10 شموع)
      if (candles.length >= 10) {
        final closes = candles.sublist(candles.length-10).map((c) => c.close).toList();
        shortTermRecommendation = SimpleAIPredictor.predictDirection(closes);
        double mean = closes.reduce((a, b) => a + b) / closes.length;
        double std = closes.length > 1 ? sqrt(closes.map((c) => pow(c - mean, 2)).reduce((a, b) => a + b) / closes.length) : 0;
        shortTermConfidence = mean != 0 ? (1 - (std / mean)).clamp(0, 1) : null;
        shortTermReport = 'توصية قصيرة الأجل: $shortTermRecommendation\nنسبة الثقة: ${(shortTermConfidence!*100).toStringAsFixed(1)}%';
      }
      // توصية طويلة الأجل (آخر 30 شمعة)
      if (candles.length >= 30) {
        final closes = candles.sublist(candles.length-30).map((c) => c.close).toList();
        longTermRecommendation = SimpleAIPredictor.predictDirection(closes);
        double mean = closes.reduce((a, b) => a + b) / closes.length;
        double std = closes.length > 1 ? sqrt(closes.map((c) => pow(c - mean, 2)).reduce((a, b) => a + b) / closes.length) : 0;
        longTermConfidence = mean != 0 ? (1 - (std / mean)).clamp(0, 1) : null;
        longTermReport = 'توصية طويلة الأجل: $longTermRecommendation\nنسبة الثقة: ${(longTermConfidence!*100).toStringAsFixed(1)}%';
      }
      // جلب المؤشرات الفنية المتقدمة
      if (candles.length > 30) {
        final open = candles.map((c) => c.open).toList();
        final high = candles.map((c) => c.high).toList();
        final low = candles.map((c) => c.low).toList();
        final close = candles.map((c) => c.close).toList();
        final volume = List<double>.filled(candles.length, 1000); // مؤقتاً إذا لم تتوفر بيانات حجم حقيقي
        taIndicators = await AIBridge.getTAIndicators(open, high, low, close, volume);
      }
      setState(() {
        loading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        loading = false;
      });
    }
  }

  Future<void> fetchYahoo() async {
    final data = await FundamentalAnalysis.fetchYahooFinance('BTC-USD');
    setState(() {
      yahooData = data;
    });
  }

  Future<void> exportToCSV() async {
    if (candles.isEmpty) return;
    final buffer = StringBuffer();
    buffer.writeln('time,open,high,low,close');
    for (final c in candles) {
      buffer.writeln('${c.time},${c.open},${c.high},${c.low},${c.close}');
    }
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/btc_chart.csv');
      await file.writeAsString(buffer.toString());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تم حفظ الملف: ${file.path}')));
      // يمكن فتح الملف تلقائياً باستخدام open_file أو similar إذا رغبت
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ في الحفظ: $e')));
    }
  }

  Future<void> exportToPDF() async {
    if (candles.isEmpty) return;
    final pdf = pw.Document();
    pdf.addPage(
      pw.Page(
        build: (pw.Context context) => pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('BTC/USDT Candlestick Data', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 8),
            pw.Table.fromTextArray(
              headers: ['Time', 'Open', 'High', 'Low', 'Close'],
              data: [
                for (final c in candles)
                  [c.time.toString(), c.open.toString(), c.high.toString(), c.low.toString(), c.close.toString()]
              ],
              cellStyle: const pw.TextStyle(fontSize: 8),
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
            ),
          ],
        ),
      ),
    );
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/btc_chart.pdf');
      await file.writeAsBytes(await pdf.save());
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تم حفظ PDF: ${file.path}')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ في الحفظ: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    // إبراز المؤشرات الأكثر تأثيرًا حسب SHAP
    List<String> topFeatures = [];
    if (widget.shapFeatureImportances != null) {
      final sorted = widget.shapFeatureImportances!.entries.toList()
        ..sort((a, b) => b.value.abs().compareTo(a.value.abs()));
      topFeatures = sorted.take(3).map((e) => e.key).toList();
    }
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // بيانات التحليل الأساسي من Yahoo Finance
          if (yahooData != null)
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Yahoo Finance:', style: TextStyle(fontWeight: FontWeight.bold)),
                    Text('السعر الحالي: ${yahooData!['regularMarketPrice'] ?? '-'} USD'),
                    Text('التغير اليومي: ${yahooData!['regularMarketChangePercent']?.toStringAsFixed(2) ?? '-'}%'),
                    Text('أعلى سعر اليوم: ${yahooData!['regularMarketDayHigh'] ?? '-'}'),
                    Text('أقل سعر اليوم: ${yahooData!['regularMarketDayLow'] ?? '-'}'),
                  ],
                ),
              ),
            ),
          // عرض توصية الذكاء الاصطناعي ونسبة الثقة من شاشة التحليل إذا توفرت
          if (widget.aiRecommendation != null && widget.aiConfidence != null)
            Card(
              color: widget.aiConfidence! > 0.8 ? Colors.green[50] : Colors.orange[50],
              child: ListTile(
                leading: Icon(
                  widget.aiRecommendation == 'صعود' ? Icons.arrow_upward : widget.aiRecommendation == 'هبوط' ? Icons.arrow_downward : Icons.trending_flat,
                  color: widget.aiRecommendation == 'صعود' ? Colors.green : widget.aiRecommendation == 'هبوط' ? Colors.red : Colors.orange,
                  size: 32,
                ),
                title: Text('توصية AI: ${widget.aiRecommendation!}', style: TextStyle(fontWeight: FontWeight.bold)),
                subtitle: Text('نسبة الثقة: ${(widget.aiConfidence! * 100).toStringAsFixed(1)}%'),
                trailing: widget.aiConfidence! > 0.8 ? const Icon(Icons.star, color: Colors.amber) : null,
              ),
            ),
          if (lstmPrediction != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('توقع LSTM للسعر القادم: ${lstmPrediction!.toStringAsFixed(2)}', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blueGrey)),
                  if (lstmConfidence != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0),
                      child: Text('الثقة: ${(lstmConfidence! * 100).toStringAsFixed(1)}%', style: const TextStyle(fontSize: 14, color: Colors.deepPurple)),
                    ),
                  if (lstmPrediction != null && candles.isNotEmpty)
                    Icon(
                      lstmPrediction! > candles.last.close ? Icons.arrow_upward : Icons.arrow_downward,
                      color: lstmPrediction! > candles.last.close ? Colors.green : Colors.red,
                      size: 28,
                    ),
                ],
              ),
            ),
          if (isHeadAndShoulders != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text(isHeadAndShoulders! ? 'نمط رأس وكتفين مكتشف' : 'لا يوجد نمط رأس وكتفين', style: TextStyle(fontSize: 14, color: isHeadAndShoulders! ? Colors.red : Colors.grey)),
            ),
          if (isWedge != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text(isWedge! ? 'نمط وتد مكتشف' : 'لا يوجد نمط وتد', style: TextStyle(fontSize: 14, color: isWedge! ? Colors.orange : Colors.grey)),
            ),
          if (monteCarloFinal != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text('توقع Monte Carlo بعد 20 خطوة: ${monteCarloFinal!.toStringAsFixed(2)}', style: const TextStyle(fontSize: 14, color: Colors.brown)),
            ),
          if (backtestResult != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text('نتيجة Backtesting: ${backtestResult!.toStringAsFixed(2)} USD', style: const TextStyle(fontSize: 14, color: Colors.green)),
            ),
          if (xaiExplanation != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text('شرح XAI: $xaiExplanation', style: const TextStyle(fontSize: 13, color: Colors.deepOrange)),
            ),
          if (detectedPattern != null && detectedPattern != 'None')
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text('النمط الفني المكتشف: $detectedPattern', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.orange)),
            ),
          if (detectedPatternML != null && detectedPatternML != 'None')
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text('النمط الفني (ML): $detectedPatternML', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue)),
            ),
          if (shortTermReport != null)
            Card(
              margin: const EdgeInsets.symmetric(vertical: 4),
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(shortTermReport!, style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
              ),
            ),
          if (longTermReport != null)
            Card(
              margin: const EdgeInsets.symmetric(vertical: 4),
              color: Colors.green[50],
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(longTermReport!, style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.green)),
              ),
            ),
          if (recommendationReport != null)
            Card(
              margin: const EdgeInsets.symmetric(vertical: 8),
              color: Colors.grey[50],
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text('تقرير التوصية', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.lightbulb_outline),
                          label: const Text('شرح XAI'),
                          style: ElevatedButton.styleFrom(backgroundColor: Colors.deepOrange, foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4), textStyle: const TextStyle(fontSize: 13)),
                          onPressed: () {
                            setState(() => showXaiDialog = true);
                          },
                        ),
                      ],
                    ),
                    SelectableText(recommendationReport!),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.copy),
                          tooltip: 'نسخ التقرير',
                          onPressed: () {
                            Clipboard.setData(ClipboardData(text: recommendationReport!));
                            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم نسخ التقرير')));
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.share),
                          tooltip: 'مشاركة التقرير',
                          onPressed: () {
                            Share.share(recommendationReport!, subject: 'تقرير توصية التداول');
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          if (showXaiDialog)
            XaiExplanationDialog(
              onClose: () => setState(() => showXaiDialog = false),
              reasons: recommendationReport,
              detectedPattern: detectedPattern,
              aiRecommendation: widget.aiRecommendation,
              lstmConfidence: widget.aiConfidence,
              xaiExplanation: xaiExplanation,
            ),
          if (gannResults != null) ...[
            Card(
              color: Colors.yellow[50],
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('تحليل Gann والدورات الزمنية:', style: TextStyle(fontWeight: FontWeight.bold)),
                    if (gannResults!['cycles'] != null)
                      Text('الدورات الزمنية الرئيسية: ${gannResults!['cycles'].map((c) => c.toStringAsFixed(1)).join(", ")}'),
                    if (gannResults!['turning_points'] != null)
                      Text('نقاط التحول (قمم): ${gannResults!['turning_points']['peaks']} | (قيعان): ${gannResults!['turning_points']['troughs']}'),
                  ],
                ),
              ),
            ),
          ],
          if (shapImportances != null)
            Card(
              color: Colors.deepPurple[50],
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('شرح الذكاء الاصطناعي (XAI - SHAP):', style: TextStyle(fontWeight: FontWeight.bold)),
                    Text('أهم الميزات المؤثرة في التوصية:'),
                    ...shapImportances!.asMap().entries.take(5).map((e) => Text('الميزة ${e.key+1}: ${e.value.toStringAsFixed(3)}')),
                    const SizedBox(height: 8),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.info_outline),
                      label: const Text('تفاصيل الشرح'),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('شرح XAI (SHAP)'),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('تأثير كل ميزة (أهمية نسبية):'),
                                ...shapImportances!.asMap().entries.map((e) => Text('الميزة ${e.key+1}: ${e.value.toStringAsFixed(3)}')),
                              ],
                            ),
                            actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('إغلاق'))],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          const Text('BTC/USDT Candlestick Chart', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          if (loading)
            const CircularProgressIndicator()
          else if (error != null)
            Text('Error: $error', style: const TextStyle(color: Colors.red))
          else
            Expanded(
              child: Screenshot(
                controller: screenshotController,
                child: ReorderableListView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  onReorder: (oldIndex, newIndex) {
                    setState(() {
                      if (newIndex > oldIndex) newIndex--;
                      final item = indicatorOrder.removeAt(oldIndex);
                      indicatorOrder.insert(newIndex, item);
                      saveIndicatorPrefs();
                    });
                  },
                  children: [
                    for (final ind in indicatorOrder)
                      if (indicatorVisibility[ind] ?? false)
                        ListTile(
                          key: ValueKey(ind),
                          title: Row(
                            children: [
                              Text('مؤشر $ind'),
                              const SizedBox(width: 8),
                              GestureDetector(
                                onTap: () => pickIndicatorColor(ind),
                                child: CircleAvatar(backgroundColor: indicatorColors[ind], radius: 12),
                              ),
                            ],
                          ),
                          trailing: Switch(
                            value: indicatorVisibility[ind] ?? false,
                            onChanged: (v) {
                              setState(() {
                                indicatorVisibility[ind] = v;
                                saveIndicatorPrefs();
                              });
                            },
                          ),
                        ),
                  ],
                ),
              ),
            ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: fetchCandles,
                child: const Text('تحديث الشارت'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: exportToCSV,
                child: const Text('تصدير CSV'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: exportToPDF,
                child: const Text('تصدير PDF'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  if (candles.isEmpty) return;
                  final csv = 'time,open,high,low,close\n' +
                      candles.map((c) => '${c.time},${c.open},${c.high},${c.low},${c.close}').join('\n');
                  Share.share(csv, subject: 'بيانات الشموع');
                },
                child: const Text('مشاركة'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: saveChartAsImage,
                child: const Text('حفظ كصورة'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text('تخصيص المؤشرات', style: TextStyle(fontWeight: FontWeight.bold)),
                              ReorderableListView(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                onReorder: (oldIndex, newIndex) {
                                  setState(() {
                                    if (newIndex > oldIndex) newIndex--;
                                    final item = indicatorOrder.removeAt(oldIndex);
                                    indicatorOrder.insert(newIndex, item);
                                    saveIndicatorPrefs();
                                  });
                                },
                                children: [
                                  for (final ind in indicatorOrder)
                                    ListTile(
                                      key: ValueKey(ind),
                                      title: Text('مؤشر $ind'),
                                      trailing: Switch(
                                        value: indicatorVisibility[ind] ?? false,
                                        onChanged: (v) {
                                          setState(() {
                                            indicatorVisibility[ind] = v;
                                            saveIndicatorPrefs();
                                          });
                                        },
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('تخصيص'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(MaterialPageRoute(builder: (_) => const ARScreen()));
                },
                child: const Text('الواقع المعزز'),
              ),
            ],
          ),
          VoiceCommandWidget(
            onCommand: (cmd) {
              if (cmd == 'refresh') fetchCandles();
              if (cmd == 'export') exportToCSV();
              if (cmd == 'dark' || cmd == 'light') setState(() {});
              if (cmd == 'dashboard') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const DashboardScreen()));
              if (cmd == 'charts') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const ChartsScreen()));
              if (cmd == 'analysis') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const AnalysisScreen()));
              if (cmd == 'trading') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const TradingScreen()));
              if (cmd == 'portfolio') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const PortfolioScreen()));
              if (cmd == 'settings') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const SettingsScreen()));
              if (cmd == 'help') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const HelpScreen()));
              if (cmd == 'logout') Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const LogoutScreen()));
            },
          ),
          // تظليل أو علامة توصية على الشارت إذا كانت الثقة مرتفعة
          if (widget.aiConfidence != null && widget.aiConfidence! > 0.8)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Text('🚨 توصية قوية: ${widget.aiRecommendation!}', style: const TextStyle(fontSize: 16, color: Colors.green, fontWeight: FontWeight.bold)),
            ),
          if (topFeatures.isNotEmpty)
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('أهم المؤشرات المؤثرة في التوصية (XAI):', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...topFeatures.map((f) => Row(
                      children: [
                        Icon(Icons.bolt, color: Colors.deepOrange),
                        const SizedBox(width: 6),
                        Text('$f', style: const TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Text('(${widget.shapFeatureImportances![f]!.toStringAsFixed(2)})', style: const TextStyle(color: Colors.blueGrey)),
                      ],
                    )),
                  ],
                ),
              ),
            ),
          if (candles.isNotEmpty)
            SizedBox(
              height: 320,
              child: Stack(
                children: [
                  CandlestickChartWidget(
                    candles: candles,
                    closeColor: closeColor,
                    smaColor: smaColor,
                    emaColor: emaColor,
                    rsiColor: rsiColor,
                    macdColor: macdColor,
                    bollColor: bollColor,
                    showSMA: showSMA,
                    showEMA: showEMA,
                    showRSI: showRSI,
                    showMACD: showMACD,
                    showBoll: showBoll,
                    entryPrice: entryPrice,
                    stopLoss: stopLoss,
                    takeProfit: takeProfit,
                    taIndicators: taIndicators,
                    showStochastic: showStochastic,
                    showATR: showATR,
                    showOBV: showOBV,
                    showWilliamsR: showWilliamsR,
                    showVPT: showVPT,
                    showMomentum: showMomentum,
                    showDemark: showDemark,
                    showAD: showAD,
                    showIchimoku: showIchimoku,
                    showFibonacci: showFibonacci,
                    showParabolicSAR: showParabolicSAR,
                    highlightFeatures: widget.shapFeatureImportances?.keys.toList(),
                    gannAngles: gannResults?['gann_angles'],
                    turningPoints: gannResults?['turning_points'],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // إشعار تداول ذكي عند تحقق توصية قوية
  @override
  void didUpdateWidget(covariant ChartsScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.aiConfidence != null && widget.aiConfidence! > 0.8 && widget.aiRecommendation != null) {
      WearService.sendWearNotification('توصية قوية', 'توصية: ${widget.aiRecommendation!} (ثقة ${(widget.aiConfidence! * 100).toStringAsFixed(1)}%)');
    }
  }

  void pickColor(String type) {
    Color current = smaColor;
    switch (type) {
      case 'SMA': current = smaColor; break;
      case 'EMA': current = emaColor; break;
      case 'RSI': current = rsiColor; break;
      case 'MACD': current = macdColor; break;
      case 'BOLL': current = bollColor; break;
    }
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللون'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: current,
            onColorChanged: (color) {
              setState(() {
                switch (type) {
                  case 'SMA': smaColor = color; break;
                  case 'EMA': emaColor = color; break;
                  case 'RSI': rsiColor = color; break;
                  case 'MACD': macdColor = color; break;
                  case 'BOLL': bollColor = color; break;
                }
              });
            },
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('تم'))],
      ),
    );
  }

  void pickIndicatorColor(String key) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('اختر لون $key'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: indicatorColors[key]!,
            onColorChanged: (color) {
              setState(() {
                indicatorColors[key] = color;
                saveIndicatorColorPrefs();
              });
            },
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('تم'))],
      ),
    );
  }

  void resetIndicatorColors() {
    setState(() {
      indicatorColors = {
        'SMA': Colors.orange,
        'EMA': Colors.green,
        'RSI': Colors.purple,
        'MACD': Colors.red,
        'Bollinger': Colors.teal,
        'Stochastic': Colors.purple,
        'ATR': Colors.brown,
        'OBV': Colors.indigo,
        'WilliamsR': Colors.deepOrange,
        'VPT': Colors.teal,
        'Momentum': Colors.pink,
        'Demark': Colors.lime,
        'AD': Colors.cyan,
        'Ichimoku': Colors.deepPurple,
        'Fibonacci': Colors.amber,
        'ParabolicSAR': Colors.black,
      };
      saveIndicatorColorPrefs();
    });
  }

  Future<void> _exportChartAsImage() async {
    try {
      RenderRepaintBoundary boundary = _chartKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/chart.png');
      await file.writeAsBytes(pngBytes);
      await Share.shareFiles([file.path], text: 'صورة الشارت');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ في تصدير الصورة: $e')));
    }
  }

  Future<void> _exportDataAsCSV() async {
    List<List<dynamic>> rows = [
      ['x', 'y'],
      ...candles.map((c) => [c.time.toDouble(), c.close]),
    ];
    String csvData = const ListToCsvConverter().convert(rows);
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/chart.csv');
    await file.writeAsString(csvData);
    await Share.shareFiles([file.path], text: 'بيانات الشارت (CSV)');
  }

  Future<void> _exportDataAsPDF() async {
    final pdfDoc = pw.Document();
    pdfDoc.addPage(
      pw.Page(
        build: (pw.Context context) => pw.Column(
          children: [
            pw.Text('BTC/USDT Candlestick Data', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 8),
            pw.Table.fromTextArray(
              data: [
                for (final c in candles)
                  [c.time.toString(), c.close.toString()],
              ],
            ),
          ],
        ),
      ),
    );
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/chart.pdf');
    await file.writeAsBytes(await pdfDoc.save());
    await Share.shareFiles([file.path], text: 'بيانات الشارت (PDF)');
  }
}

class CandleData {
  final double open, high, low, close;
  final int time;
  CandleData({required this.open, required this.high, required this.low, required this.close, required this.time});
  factory CandleData.fromList(List<dynamic> l) {
    return CandleData(
      time: l[0],
      open: double.parse(l[1]),
      high: double.parse(l[2]),
      low: double.parse(l[3]),
      close: double.parse(l[4]),
    );
  }
}

class CandlestickChartWidget extends StatelessWidget {
  final List<CandleData> candles;
  final Color closeColor, smaColor, emaColor, rsiColor, macdColor, bollColor;
  final bool showSMA, showEMA, showRSI, showMACD, showBoll;
  final double? entryPrice;
  final double? stopLoss;
  final double? takeProfit;
  final Map<String, dynamic>? taIndicators;
  final bool showStochastic, showATR, showOBV, showWilliamsR, showVPT, showMomentum, showDemark, showAD, showIchimoku, showFibonacci, showParabolicSAR;
  final List<String>? highlightFeatures;
  final List<double>? gannAngles;
  final Map<String, dynamic>? turningPoints;
  const CandlestickChartWidget({
    required this.candles,
    this.closeColor = Colors.blue,
    this.smaColor = Colors.orange,
    this.emaColor = Colors.green,
    this.rsiColor = Colors.purple,
    this.macdColor = Colors.red,
    this.bollColor = Colors.teal,
    this.showSMA = true,
    this.showEMA = true,
    this.showRSI = true,
    this.showMACD = true,
    this.showBoll = true,
    this.entryPrice,
    this.stopLoss,
    this.takeProfit,
    this.taIndicators,
    this.showStochastic = false,
    this.showATR = false,
    this.showOBV = false,
    this.showWilliamsR = false,
    this.showVPT = false,
    this.showMomentum = false,
    this.showDemark = false,
    this.showAD = false,
    this.showIchimoku = false,
    this.showFibonacci = false,
    this.showParabolicSAR = false,
    this.highlightFeatures,
    this.gannAngles,
    this.turningPoints,
    super.key,
  });

  List<FlSpot> getSMA(int period) {
    List<FlSpot> spots = [];
    for (int i = period - 1; i < candles.length; i++) {
      double sum = 0;
      for (int j = 0; j < period; j++) {
        sum += candles[i - j].close;
      }
      spots.add(FlSpot(i.toDouble(), sum / period));
    }
    return spots;
  }

  List<FlSpot> getEMA(int period) {
    List<FlSpot> spots = [];
    double? ema;
    double k = 2 / (period + 1);
    for (int i = 0; i < candles.length; i++) {
      double close = candles[i].close;
      if (ema == null) {
        ema = close;
      } else {
        ema = close * k + ema * (1 - k);
      }
      if (i >= period - 1) {
        spots.add(FlSpot(i.toDouble(), ema));
      }
    }
    return spots;
  }

  List<FlSpot> getRSI({int period = 14}) {
    List<FlSpot> spots = [];
    for (int i = period; i < candles.length; i++) {
      double gain = 0, loss = 0;
      for (int j = i - period + 1; j <= i; j++) {
        double change = candles[j].close - candles[j - 1].close;
        if (change > 0) gain += change;
        else loss -= change;
      }
      double rs = loss == 0 ? 100 : gain / loss;
      double rsi = 100 - (100 / (1 + rs));
      spots.add(FlSpot(i.toDouble(), rsi));
    }
    return spots;
  }

  List<FlSpot> getMACD({int fast = 12, int slow = 26, int signal = 9}) {
    List<double> emaFast = [];
    List<double> emaSlow = [];
    double? fastEma, slowEma;
    double kFast = 2 / (fast + 1);
    double kSlow = 2 / (slow + 1);
    for (int i = 0; i < candles.length; i++) {
      double close = candles[i].close;
      if (fastEma == null) fastEma = close;
      else fastEma = close * kFast + fastEma * (1 - kFast);
      if (slowEma == null) slowEma = close;
      else slowEma = close * kSlow + slowEma * (1 - kSlow);
      emaFast.add(fastEma);
      emaSlow.add(slowEma);
    }
    List<FlSpot> macd = [];
    for (int i = 0; i < candles.length; i++) {
      macd.add(FlSpot(i.toDouble(), emaFast[i] - emaSlow[i]));
    }
    return macd;
  }

  List<FlSpot> getBollingerUpper({int period = 20, double mult = 2}) {
    List<FlSpot> spots = [];
    for (int i = period - 1; i < candles.length; i++) {
      double sum = 0;
      for (int j = 0; j < period; j++) {
        sum += candles[i - j].close;
      }
      double mean = sum / period;
      double std = 0;
      for (int j = 0; j < period; j++) {
        std += pow(candles[i - j].close - mean, 2);
      }
      std = sqrt(std / period);
      spots.add(FlSpot(i.toDouble(), mean + mult * std));
    }
    return spots;
  }

  List<FlSpot> getBollingerLower({int period = 20, double mult = 2}) {
    List<FlSpot> spots = [];
    for (int i = period - 1; i < candles.length; i++) {
      double sum = 0;
      for (int j = 0; j < period; j++) {
        sum += candles[i - j].close;
      }
      double mean = sum / period;
      double std = 0;
      for (int j = 0; j < period; j++) {
        std += pow(candles[i - j].close - mean, 2);
      }
      std = sqrt(std / period);
      spots.add(FlSpot(i.toDouble(), mean - mult * std));
    }
    return spots;
  }

  @override
  Widget build(BuildContext context) {
    if (candles.isEmpty) return const SizedBox();
    double minY = candles.map((e) => e.low).reduce(min);
    double maxY = candles.map((e) => e.high).reduce(max);
    List<HorizontalLine> extraLines = [];
    if (entryPrice != null) {
      extraLines.add(HorizontalLine(
        y: entryPrice!,
        color: Colors.green,
        strokeWidth: 2,
        dashArray: [6, 2],
        label: HorizontalLineLabel(
          show: true,
          alignment: Alignment.centerLeft,
          style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
          labelResolver: (_) => 'Entry: ${entryPrice!.toStringAsFixed(2)}',
        ),
      ));
    }
    if (stopLoss != null) {
      extraLines.add(HorizontalLine(
        y: stopLoss!,
        color: Colors.red,
        strokeWidth: 2,
        dashArray: [4, 4],
        label: HorizontalLineLabel(
          show: true,
          alignment: Alignment.centerLeft,
          style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          labelResolver: (_) => 'Stop Loss: ${stopLoss!.toStringAsFixed(2)}',
        ),
      ));
    }
    if (takeProfit != null) {
      extraLines.add(HorizontalLine(
        y: takeProfit!,
        color: Colors.blue,
        strokeWidth: 2,
        dashArray: [2, 6],
        label: HorizontalLineLabel(
          show: true,
          alignment: Alignment.centerLeft,
          style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
          labelResolver: (_) => 'Take Profit: ${takeProfit!.toStringAsFixed(2)}',
        ),
      ));
    }
    List<LineChartBarData> extraIndicators = [];
    if (taIndicators != null) {
      if (showStochastic && taIndicators!['stochastic_k'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['stochastic_k'].length; i++) FlSpot(i.toDouble(), taIndicators!['stochastic_k'][i] ?? 0)],
          isCurved: false,
          color: Colors.purple,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showATR && taIndicators!['atr'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['atr'].length; i++) FlSpot(i.toDouble(), taIndicators!['atr'][i] ?? 0)],
          isCurved: false,
          color: Colors.brown,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showOBV && taIndicators!['obv'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['obv'].length; i++) FlSpot(i.toDouble(), taIndicators!['obv'][i] ?? 0)],
          isCurved: false,
          color: Colors.indigo,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showWilliamsR && taIndicators!['williams_r'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['williams_r'].length; i++) FlSpot(i.toDouble(), taIndicators!['williams_r'][i] ?? 0)],
          isCurved: false,
          color: Colors.deepOrange,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showVPT && taIndicators!['vpt'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['vpt'].length; i++) FlSpot(i.toDouble(), taIndicators!['vpt'][i] ?? 0)],
          isCurved: false,
          color: Colors.teal,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showMomentum && taIndicators!['momentum'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['momentum'].length; i++) FlSpot(i.toDouble(), taIndicators!['momentum'][i] ?? 0)],
          isCurved: false,
          color: Colors.pink,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showDemark && taIndicators!['demark'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['demark'].length; i++) FlSpot(i.toDouble(), taIndicators!['demark'][i] ?? 0)],
          isCurved: false,
          color: Colors.lime,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showAD && taIndicators!['ad'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['ad'].length; i++) FlSpot(i.toDouble(), taIndicators!['ad'][i] ?? 0)],
          isCurved: false,
          color: Colors.cyan,
          dotData: FlDotData(show: false),
          barWidth: 1.5,
        ));
      }
      if (showIchimoku && taIndicators!['ichimoku'] != null) {
        final ichimoku = taIndicators!['ichimoku'];
        extraLines.add(HorizontalLine(y: ichimoku['tenkan_sen'] ?? 0, color: Colors.deepPurple, strokeWidth: 2, label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Tenkan')));
        extraLines.add(HorizontalLine(y: ichimoku['kijun_sen'] ?? 0, color: Colors.deepOrange, strokeWidth: 2, label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Kijun')));
        extraLines.add(HorizontalLine(y: ichimoku['senkou_span_a'] ?? 0, color: Colors.green, strokeWidth: 2, label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Span A')));
        extraLines.add(HorizontalLine(y: ichimoku['senkou_span_b'] ?? 0, color: Colors.red, strokeWidth: 2, label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Span B')));
      }
      if (showFibonacci && taIndicators!['fibonacci'] != null) {
        for (final f in taIndicators!['fibonacci']) {
          extraLines.add(HorizontalLine(y: f, color: Colors.amber, strokeWidth: 1, dashArray: [2, 4], label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Fib')));
        }
      }
      if (showParabolicSAR && taIndicators!['parabolic_sar'] != null) {
        extraIndicators.add(LineChartBarData(
          spots: [for (int i = 0; i < taIndicators!['parabolic_sar'].length; i++) FlSpot(i.toDouble(), taIndicators!['parabolic_sar'][i] ?? 0)],
          isCurved: false,
          color: Colors.black,
          dotData: FlDotData(show: true, dotColor: Colors.black, dotSize: 3),
          barWidth: 1.5,
        ));
      }
    }
    // خطوط مائلة وتظليل متقدم للأنماط
    List<LineChartBarData> patternTrendlines = [];
    if (gannAngles != null) {
      for (final angle in gannAngles!) {
        extraLines.add(HorizontalLine(y: angle, color: Colors.deepPurple, strokeWidth: 2, dashArray: [4, 4], label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Gann Angle: $angle')));
      }
    }
    if (turningPoints != null) {
      if (turningPoints!['peaks'] != null) {
        for (final peak in turningPoints!['peaks']) {
          extraLines.add(HorizontalLine(y: peak, color: Colors.deepPurple, strokeWidth: 2, dashArray: [4, 4], label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Peak: $peak')));
        }
      }
      if (turningPoints!['troughs'] != null) {
        for (final trough in turningPoints!['troughs']) {
          extraLines.add(HorizontalLine(y: trough, color: Colors.deepPurple, strokeWidth: 2, dashArray: [4, 4], label: HorizontalLineLabel(show: true, labelResolver: (_) => 'Trough: $trough')));
        }
      }
    }
    // تخصيص لون أو سمك المؤشرات المؤثرة
    Color getColor(String name, Color base) => (highlightFeatures?.contains(name) ?? false) ? Colors.deepOrange : base;
    double getWidth(String name, double base) => (highlightFeatures?.contains(name) ?? false) ? base + 2 : base;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: LineChart(
        LineChartData(
          minY: minY,
          maxY: maxY,
          titlesData: FlTitlesData(show: true),
          borderData: FlBorderData(show: true),
          lineBarsData: [
            LineChartBarData(
              spots: [for (int i = 0; i < candles.length; i++) FlSpot(i.toDouble(), candles[i].close)],
              isCurved: false,
              color: closeColor,
              dotData: FlDotData(show: false),
              barWidth: 2,
            ),
            if (showSMA)
              LineChartBarData(
                spots: getSMA(7),
                isCurved: false,
                color: getColor('SMA', smaColor),
                dotData: FlDotData(show: false),
                barWidth: getWidth('SMA', 2),
              ),
            if (showEMA)
              LineChartBarData(
                spots: getEMA(14),
                isCurved: false,
                color: getColor('EMA', emaColor),
                dotData: FlDotData(show: false),
                barWidth: getWidth('EMA', 2),
              ),
            if (showRSI)
              LineChartBarData(
                spots: getRSI(),
                isCurved: false,
                color: getColor('RSI', rsiColor),
                dotData: FlDotData(show: false),
                barWidth: getWidth('RSI', 2),
              ),
            if (showMACD)
              LineChartBarData(
                spots: getMACD(),
                isCurved: false,
                color: getColor('MACD', macdColor),
                dotData: FlDotData(show: false),
                barWidth: getWidth('MACD', 2),
              ),
            if (showBoll)
              LineChartBarData(
                spots: getBollingerUpper(),
                isCurved: false,
                color: getColor('Bollinger', bollColor),
                dotData: FlDotData(show: false),
                barWidth: getWidth('Bollinger', 2),
              ),
            if (showBoll)
              LineChartBarData(
                spots: getBollingerLower(),
                isCurved: false,
                color: getColor('Bollinger', bollColor),
                dotData: FlDotData(show: false),
                barWidth: getWidth('Bollinger', 2),
                dashArray: [4, 4],
              ),
          ] + extraIndicators + patternTrendlines,
          extraLinesData: ExtraLinesData(horizontalLines: extraLines),
        ),
      ),
    );
  }
}

class XaiExplanationDialog extends StatelessWidget {
  final VoidCallback onClose;
  final String? reasons;
  final String? detectedPattern;
  final String? aiRecommendation;
  final double? lstmConfidence;
  final String? xaiExplanation;
  const XaiExplanationDialog({super.key, required this.onClose, this.reasons, this.detectedPattern, this.aiRecommendation, this.lstmConfidence, this.xaiExplanation});
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Icon(Icons.lightbulb_outline, color: Colors.deepOrange),
                  const SizedBox(width: 8),
                  const Text('شرح XAI للتوصية', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                  const Spacer(),
                  IconButton(icon: const Icon(Icons.close), onPressed: onClose),
                ],
              ),
              const Divider(),
              if (aiRecommendation != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Text('توصية الذكاء الاصطناعي: $aiRecommendation', style: const TextStyle(color: Colors.deepPurple, fontWeight: FontWeight.bold)),
                ),
              if (lstmConfidence != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: Text('نسبة الثقة: ${(lstmConfidence!*100).toStringAsFixed(1)}%', style: const TextStyle(color: Colors.blueGrey)),
                ),
              if (detectedPattern != null && detectedPattern != 'None')
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: Text('تم اكتشاف نمط فني: $detectedPattern', style: const TextStyle(color: Colors.orange)),
                ),
              if (xaiExplanation != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: Text('تأثير آخر تغيرين في الأسعار: $xaiExplanation', style: const TextStyle(color: Colors.teal)),
                ),
              if (reasons != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text('العوامل المؤثرة في التوصية:', style: const TextStyle(fontWeight: FontWeight.bold)),
                ),
              if (reasons != null)
                ...reasons!.split('\n').where((e) => e.trim().isNotEmpty && !e.startsWith('تقرير التوصية')).map((e) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.green, size: 18),
                      const SizedBox(width: 6),
                      Expanded(child: Text(e, style: const TextStyle(fontSize: 14))),
                    ],
                  ),
                )),
              const SizedBox(height: 8),
              const Text('شرح مبسط:', style: TextStyle(fontWeight: FontWeight.bold)),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Text(_generateSimpleExplanation()),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _generateSimpleExplanation() {
    List<String> expl = [];
    if (aiRecommendation != null) expl.add('النموذج يتوقع: $aiRecommendation.');
    if (detectedPattern != null && detectedPattern != 'None') expl.add('تم اكتشاف نمط فني: $detectedPattern.');
    if (lstmConfidence != null) expl.add('نسبة الثقة بالنموذج: ${(lstmConfidence!*100).toStringAsFixed(1)}%.');
    if (xaiExplanation != null) expl.add('آخر تغيرين في الأسعار: $xaiExplanation.');
    if (expl.isEmpty) return 'لا يوجد شرح إضافي.';
    return expl.join(' ');
  }
}

// دالة مساعدة لجلب البيانات من الإنترنت
Future<dynamic> fetchHttp(String url) async {
  final response = await fetchMarketDataHttp(url);
  return response;
} 