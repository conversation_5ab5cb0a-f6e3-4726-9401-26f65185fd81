import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:share_plus/share_plus.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import '../models/data_provider.dart';
import 'dart:convert';
import 'dart:io';

class DataCenterScreen extends StatefulWidget {
  const DataCenterScreen({super.key});
  @override
  State<DataCenterScreen> createState() => _DataCenterScreenState();
}

class _DataCenterScreenState extends State<DataCenterScreen> {
  final List<String> sources = [
    'alpha_vantage', 'coingecko', 'oanda', 'stocktwits', 'economic', 'finnhub'
  ];
  String selectedSource = 'alpha_vantage';
  String symbol = 'AAPL';
  String interval = '1min';
  String dataType = 'ohlcv'; // ohlcv, news, events
  List<Map<String, dynamic>> dataRows = [];
  List<String> compareSources = [];
  bool loading = false;
  String? alertMsg;
  String? exportMsg;

  Future<void> fetchData() async {
    setState(() { loading = true; alertMsg = null; });
    dataRows.clear();
    List<String> allSources = [selectedSource, ...compareSources];
    for (final src in allSources) {
      final params = {
        'symbol': symbol,
        'interval': interval,
        'data_type': dataType,
      };
      final result = await DataProvider.fetchData(source: src, params: params);
      if (result['ohlcv'] != null) {
        for (final row in result['ohlcv']) {
          dataRows.add({...row, 'source': src});
        }
      } else if (result['news'] != null) {
        for (final row in result['news']) {
          dataRows.add({...row, 'source': src});
        }
      } else if (result['events'] != null) {
        for (final row in result['events']) {
          dataRows.add({...row, 'source': src});
        }
      }
    }
    setState(() { loading = false; });
  }

  void exportCSV() {
    if (dataRows.isEmpty) return;
    final csv = StringBuffer();
    csv.writeln(dataRows.first.keys.join(','));
    for (final row in dataRows) {
      csv.writeln(row.values.map((v) => '"$v"').join(','));
    }
    Share.share(csv.toString(), subject: 'بيانات السوق');
    setState(() { exportMsg = 'تم تصدير CSV'; });
  }

  void exportExcel() async {
    if (dataRows.isEmpty) return;
    final workbook = xlsio.Workbook();
    final sheet = workbook.worksheets[0];
    int col = 1;
    for (final k in dataRows.first.keys) {
      sheet.getRangeByIndex(1, col++).setText(k);
    }
    for (int i = 0; i < dataRows.length; i++) {
      int c = 1;
      for (final v in dataRows[i].values) {
        sheet.getRangeByIndex(i + 2, c++).setText(v.toString());
      }
    }
    final bytes = workbook.saveAsStream();
    workbook.dispose();
    final file = File('/tmp/market_data.xlsx');
    await file.writeAsBytes(bytes, flush: true);
    Share.shareXFiles([XFile(file.path)], text: 'بيانات السوق Excel');
    setState(() { exportMsg = 'تم تصدير Excel'; });
  }

  void exportPDF() async {
    if (dataRows.isEmpty) return;
    final pdf = pw.Document();
    pdf.addPage(
      pw.Page(
        build: (context) => pw.Table.fromTextArray(
          data: [dataRows.first.keys.toList()] + dataRows.map((r) => r.values.toList()).toList(),
        ),
      ),
    );
    final file = File('/tmp/market_data.pdf');
    await file.writeAsBytes(await pdf.save(), flush: true);
    Share.shareXFiles([XFile(file.path)], text: 'بيانات السوق PDF');
    setState(() { exportMsg = 'تم تصدير PDF'; });
  }

  void checkAlerts() {
    // مثال: تنبيه إذا كان هناك خبر عاجل أو حركة سعرية قوية
    for (final row in dataRows) {
      if (row['news'] != null && row['news'].toString().contains('Breaking')) {
        setState(() { alertMsg = 'خبر عاجل: ${row['news']}'; });
        return;
      }
      if (row['close'] != null && row['open'] != null &&
          ((row['close'] - row['open']).abs() / (row['open'] + 1e-8) > 0.05)) {
        setState(() { alertMsg = 'حركة سعرية قوية في ${row['source']}'; });
        return;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('مركز البيانات المتقدم')),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text('المصدر:'),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: selectedSource,
                  items: sources.map((s) => DropdownMenuItem(value: s, child: Text(s))).toList(),
                  onChanged: (v) => setState(() => selectedSource = v!),
                ),
                const SizedBox(width: 16),
                const Text('الرمز:'),
                const SizedBox(width: 8),
                SizedBox(
                  width: 80,
                  child: TextField(
                    controller: TextEditingController(text: symbol),
                    onChanged: (v) => symbol = v,
                  ),
                ),
                const SizedBox(width: 16),
                const Text('الفاصل:'),
                const SizedBox(width: 8),
                SizedBox(
                  width: 60,
                  child: TextField(
                    controller: TextEditingController(text: interval),
                    onChanged: (v) => interval = v,
                  ),
                ),
                const SizedBox(width: 16),
                const Text('النوع:'),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: dataType,
                  items: ['ohlcv', 'news', 'events'].map((t) => DropdownMenuItem(value: t, child: Text(t))).toList(),
                  onChanged: (v) => setState(() => dataType = v!),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  icon: const Icon(Icons.search),
                  label: const Text('جلب البيانات'),
                  onPressed: fetchData,
                ),
              ],
            ),
            Row(
              children: [
                const Text('قارن مع:'),
                const SizedBox(width: 8),
                Expanded(
                  child: Wrap(
                    spacing: 8,
                    children: sources.where((s) => s != selectedSource).map((s) => FilterChip(
                      label: Text(s),
                      selected: compareSources.contains(s),
                      onSelected: (v) {
                        setState(() {
                          if (v) {
                            compareSources.add(s);
                          } else {
                            compareSources.remove(s);
                          }
                        });
                      },
                    )).toList(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (loading) const Center(child: CircularProgressIndicator()),
            if (!loading && dataRows.isNotEmpty)
              Expanded(
                child: Column(
                  children: [
                    SizedBox(
                      height: 220,
                      child: LineChart(
                        LineChartData(
                          minY: dataRows.map((e) => e['low'] ?? 0.0).reduce((a, b) => a < b ? a : b),
                          maxY: dataRows.map((e) => e['high'] ?? 0.0).reduce((a, b) => a > b ? a : b),
                          lineBarsData: [
                            for (final src in [selectedSource, ...compareSources])
                              LineChartBarData(
                                spots: [
                                  for (final row in dataRows.where((r) => r['source'] == src && r['close'] != null))
                                    FlSpot((row['time'] is num ? row['time'].toDouble() : 0.0), (row['close'] as num?)?.toDouble() ?? 0.0)
                                ],
                                isCurved: false,
                                color: src == selectedSource ? Colors.blue : Colors.orange,
                                dotData: FlDotData(show: false),
                                barWidth: 2,
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          icon: const Icon(Icons.file_copy),
                          label: const Text('تصدير CSV'),
                          onPressed: exportCSV,
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.picture_as_pdf),
                          label: const Text('تصدير PDF'),
                          onPressed: exportPDF,
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.table_chart),
                          label: const Text('تصدير Excel'),
                          onPressed: exportExcel,
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.notifications),
                          label: const Text('فحص التنبيهات'),
                          onPressed: checkAlerts,
                        ),
                      ],
                    ),
                    if (alertMsg != null)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(alertMsg!, style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold)),
                      ),
                    if (exportMsg != null)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(exportMsg!, style: const TextStyle(color: Colors.green)),
                      ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: DataTable(
                          columns: [for (final k in dataRows.first.keys) DataColumn(label: Text(k))],
                          rows: [
                            for (final row in dataRows)
                              DataRow(cells: [for (final v in row.values) DataCell(Text(v.toString()))]),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            if (!loading && dataRows.isEmpty)
              const Text('لا توجد بيانات.'),
          ],
        ),
      ),
    );
  }
} 