from flask import Flask, request, jsonify
from flask_cors import CORS
import hashlib
import base64
import os

app = Flask(__name__)
CORS(app)

# محاكاة توقيع Hyperledger Fabric (للتجربة)
def sign_transaction(data, private_key='secret_key'):
    tx_str = str(data)
    tx_hash = hashlib.sha256(tx_str.encode()).hexdigest()
    signature = base64.b64encode(hashlib.pbkdf2_hmac('sha256', tx_hash.encode(), private_key.encode(), 100000)).decode()
    return tx_hash, signature

@app.route('/sign_trade', methods=['POST'])
def sign_trade():
    data = request.json
    tx_hash, signature = sign_transaction(data)
    return jsonify({'tx_hash': tx_hash, 'signature': signature})

@app.route('/verify_trade', methods=['POST'])
def verify_trade():
    data = request.json
    tx_hash = data['tx_hash']
    signature = data['signature']
    _, expected_signature = sign_transaction({'tx_hash': tx_hash})
    return jsonify({'valid': signature == expected_signature})

if __name__ == '__main__':
    app.run(port=5009, debug=True) 