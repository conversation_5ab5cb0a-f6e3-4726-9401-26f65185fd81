from flask import Flask, request, jsonify
from flask_cors import CORS
import nashpy as nash
import numpy as np
import simpy
import threading

app = Flask(__name__)
CORS(app)

# Nashpy Game Theory Simulation
@app.route('/nashpy', methods=['POST'])
def nashpy_simulation():
    data = request.json
    A = np.array(data.get('A', [[1, -1], [-1, 1]]))
    B = np.array(data.get('B', [[-1, 1], [1, -1]]))
    game = nash.Game(A, B)
    eqs = list(game.support_enumeration())
    result = [{'row': eq[0].tolist(), 'col': eq[1].tolist()} for eq in eqs]
    return jsonify({'equilibria': result})

# SimPy Market Simulation
simpy_results = []

def market_simulation(env, results, steps=5):
    for i in range(steps):
        results.append({'step': i, 'event': f'Trader acts at {env.now}'})
        yield env.timeout(1)

@app.route('/simpy', methods=['POST'])
def simpy_simulation():
    steps = request.json.get('steps', 5)
    results = []
    env = simpy.Environment()
    env.process(market_simulation(env, results, steps))
    env.run(until=steps)
    return jsonify({'simulation': results})

if __name__ == '__main__':
    app.run(debug=True) 