import sys
import json
import numpy as np
try:
    import talib
except ImportError:
    talib = None

def parse_input():
    data = json.loads(sys.stdin.read())
    close = np.array(data['close'], dtype=float)
    high = np.array(data['high'], dtype=float)
    low = np.array(data['low'], dtype=float)
    open_ = np.array(data['open'], dtype=float)
    volume = np.array(data['volume'], dtype=float)
    return open_, high, low, close, volume

def main():
    open_, high, low, close, volume = parse_input()
    result = {}
    if talib:
        # Stochastic Oscillator
        slowk, slowd = talib.STOCH(high, low, close)
        result['stochastic_k'] = slowk.tolist()
        result['stochastic_d'] = slowd.tolist()
        # ATR
        result['atr'] = talib.ATR(high, low, close).tolist()
        # OBV
        result['obv'] = talib.OBV(close, volume).tolist()
        # Williams %R
        result['williams_r'] = talib.WILLR(high, low, close).tolist()
        # Volume Price Trend (VPT)
        vpt = [0]
        for i in range(1, len(close)):
            vpt.append(vpt[-1] + volume[i] * (close[i] - close[i-1]) / close[i-1] if close[i-1] != 0 else 0)
        result['vpt'] = vpt
        # Momentum
        result['momentum'] = talib.MOM(close, timeperiod=10).tolist()
        # Demark Indicator (TD Sequential, مبسط)
        demark = [0]*len(close)
        for i in range(4, len(close)):
            if close[i] > close[i-4]: demark[i] = demark[i-1] + 1
            else: demark[i] = 0
        result['demark'] = demark
        # Accumulation/Distribution
        result['ad'] = talib.AD(high, low, close, volume).tolist()
        # Ichimoku Kinko Hyo (Tenkan, Kijun, Senkou Span A/B)
        period9_high = np.max(high[-9:]) if len(high) >= 9 else np.nan
        period9_low = np.min(low[-9:]) if len(low) >= 9 else np.nan
        tenkan_sen = (period9_high + period9_low) / 2
        period26_high = np.max(high[-26:]) if len(high) >= 26 else np.nan
        period26_low = np.min(low[-26:]) if len(low) >= 26 else np.nan
        kijun_sen = (period26_high + period26_low) / 2
        senkou_span_a = (tenkan_sen + kijun_sen) / 2
        period52_high = np.max(high[-52:]) if len(high) >= 52 else np.nan
        period52_low = np.min(low[-52:]) if len(low) >= 52 else np.nan
        senkou_span_b = (period52_high + period52_low) / 2
        result['ichimoku'] = {
            'tenkan_sen': tenkan_sen,
            'kijun_sen': kijun_sen,
            'senkou_span_a': senkou_span_a,
            'senkou_span_b': senkou_span_b
        }
        # Fibonacci Retracement (مستويات 0, 0.236, 0.382, 0.5, 0.618, 1)
        max_price = np.max(high)
        min_price = np.min(low)
        diff = max_price - min_price
        fibs = [max_price - diff * r for r in [0, 0.236, 0.382, 0.5, 0.618, 1]]
        result['fibonacci'] = fibs
        # Parabolic SAR
        result['parabolic_sar'] = talib.SAR(high, low).tolist()
    else:
        result['error'] = 'TA-Lib not installed.'
    print(json.dumps(result))

if __name__ == '__main__':
    main() 