from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
from sklearn.ensemble import RandomForestClassifier
import joblib

app = Flask(__name__)
CORS(app)

# نموذج مبسط (يجب تدريب نموذج حقيقي ببيانات أكبر)
try:
    model = joblib.load('pattern_model.pkl')
except:
    # نموذج عشوائي مؤقت
    class DummyModel:
        def predict(self, X):
            return np.random.choice(['Head & Shoulders', 'Wedge', 'Triangle', 'Flag', 'Cup & Handle', 'None'], size=len(X))
    model = DummyModel()

@app.route('/detect_pattern', methods=['POST'])
def detect_pattern():
    data = request.json
    closes = np.array(data['close'], dtype=float)
    # ميزات مبسطة: الفروق، المتوسط، الانحراف المعياري
    features = np.array([
        closes[-1] - closes[0],
        np.mean(closes),
        np.std(closes),
        np.max(closes) - np.min(closes),
    ]).reshape(1, -1)
    pattern = model.predict(features)[0]
    return jsonify({'pattern': pattern})

if __name__ == '__main__':
    app.run(port=5002, debug=True) 