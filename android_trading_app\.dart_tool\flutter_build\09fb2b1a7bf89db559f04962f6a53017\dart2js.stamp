{"inputs": ["D:\\ai 300\\flutter\\bin\\cache\\engine.stamp", "D:\\ai 300\\flutter\\bin\\cache\\engine.stamp", "D:\\ai 300\\android_trading_app\\.dart_tool\\flutter_build\\09fb2b1a7bf89db559f04962f6a53017\\main.dart", "D:\\ai 300\\android_trading_app\\.dart_tool\\package_config_subset", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "D:\\ai 300\\android_trading_app\\.dart_tool\\flutter_build\\09fb2b1a7bf89db559f04962f6a53017\\main.dart", "D:\\ai 300\\android_trading_app\\.dart_tool\\flutter_build\\09fb2b1a7bf89db559f04962f6a53017\\web_plugin_registrant.dart", "D:\\ai 300\\android_trading_app\\.dart_tool\\package_config.json", "D:\\ai 300\\android_trading_app\\lib\\main.dart", "D:\\ai 300\\flutter\\bin\\cache\\dart-sdk\\lib\\libraries.json", "D:\\ai 300\\flutter\\bin\\cache\\flutter_web_sdk\\kernel\\dart2js_platform.dill", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\animation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\cupertino.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\foundation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\gestures.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\material.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\painting.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\physics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\rendering.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\scheduler.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\semantics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\services.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\flutter_version.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_web.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\expansible.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "D:\\ai 300\\flutter\\packages\\flutter\\lib\\widgets.dart"], "outputs": ["D:\\ai 300\\android_trading_app\\.dart_tool\\flutter_build\\09fb2b1a7bf89db559f04962f6a53017\\main.dart.js", "D:\\ai 300\\android_trading_app\\.dart_tool\\flutter_build\\09fb2b1a7bf89db559f04962f6a53017\\main.dart.js"], "buildKey": "{\"optimizationLevel\":null,\"webRenderer\":\"canvaskit\",\"csp\":false,\"dumpInfo\":false,\"nativeNullAssertions\":true,\"noFrequencyBasedMinification\":false,\"sourceMaps\":false}"}