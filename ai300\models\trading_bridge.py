import os
import ccxt
from dotenv import load_dotenv

load_dotenv()

BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
BINANCE_API_SECRET = os.getenv('BINANCE_API_SECRET')


def create_binance_client(testnet=True):
    binance = ccxt.binance({
        'apiKey': BINANCE_API_KEY,
        'secret': BINANCE_API_SECRET,
        'enableRateLimit': True,
        'options': {'defaultType': 'future'},
    })
    if testnet:
        binance.set_sandbox_mode(True)
    return binance


def place_order(symbol, side, amount, testnet=True):
    client = create_binance_client(testnet)
    try:
        order = client.create_market_order(symbol, side, amount)
        return {'status': 'success', 'order': order}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}


def place_limit_order(symbol, side, amount, price, testnet=True):
    client = create_binance_client(testnet)
    try:
        order = client.create_limit_order(symbol, side, amount, price)
        return {'status': 'success', 'order': order}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}


def place_stop_order(symbol, side, amount, stop_price, testnet=True):
    client = create_binance_client(testnet)
    try:
        params = {'stopPrice': stop_price}
        order = client.create_order(symbol=symbol, type='STOP_MARKET', side=side, amount=amount, params=params)
        return {'status': 'success', 'order': order}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}


def get_balance(testnet=True):
    client = create_binance_client(testnet)
    try:
        balance = client.fetch_balance()
        return {'status': 'success', 'balance': balance}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}


def fetch_orders(symbol=None, testnet=True):
    client = create_binance_client(testnet)
    try:
        orders = client.fetch_orders(symbol) if symbol else client.fetch_orders()
        return {'status': 'success', 'orders': orders}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}


if __name__ == '__main__':
    import sys
    import json
    args = sys.argv[1:]
    if args and args[0] == 'order':
        symbol = args[1]
        side = args[2]
        amount = float(args[3])
        testnet = args[4].lower() == 'true' if len(args) > 4 else True
        result = place_order(symbol, side, amount, testnet)
        print(json.dumps(result))
    elif args and args[0] == 'limit':
        symbol, side, amount, price = args[1], args[2], float(args[3]), float(args[4])
        testnet = args[5].lower() == 'true' if len(args) > 5 else True
        result = place_limit_order(symbol, side, amount, price, testnet)
        print(json.dumps(result))
    elif args and args[0] == 'stop':
        symbol, side, amount, stop_price = args[1], args[2], float(args[3]), float(args[4])
        testnet = args[5].lower() == 'true' if len(args) > 5 else True
        result = place_stop_order(symbol, side, amount, stop_price, testnet)
        print(json.dumps(result))
    elif args and args[0] == 'balance':
        testnet = args[1].lower() == 'true' if len(args) > 1 else True
        result = get_balance(testnet)
        print(json.dumps(result))
    elif args and args[0] == 'orders':
        symbol = args[1] if len(args) > 2 else None
        testnet = args[-1].lower() == 'true' if len(args) > 1 else True
        result = fetch_orders(symbol, testnet)
        print(json.dumps(result))
    elif args and args[0] == 'arbitrage':
        symbol = args[1] if len(args) > 1 else 'BTC/USDT'
        amount = float(args[2]) if len(args) > 2 else 0.01
        min_profit = float(args[3]) if len(args) > 3 else 1
        testnet = args[4].lower() == 'true' if len(args) > 4 else True
        import arbitrage_engine
        result = arbitrage_engine.execute_arbitrage(symbol, amount, min_profit, testnet)
        print(json.dumps(result))
    else:
        print(json.dumps({'status': 'error', 'error': 'Invalid command'})) 