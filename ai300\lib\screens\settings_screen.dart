import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../ai_models.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});
  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String fontFamily = 'Cairo';
  double fontSize = 16;
  Color primaryColor = Colors.deepPurple;
  Color accentColor = Colors.amber;
  bool darkMode = false;
  ModelType selected = AIModelManager.selectedModel;
  bool autoML = AIModelManager.useAutoML;
  String? selectedLanguage;

  @override
  void initState() {
    super.initState();
    loadPrefs();
    selectedLanguage = Localizations.localeOf(context).languageCode;
  }

  Future<void> loadPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      fontFamily = prefs.getString('fontFamily') ?? 'Cairo';
      fontSize = prefs.getDouble('fontSize') ?? 16;
      primaryColor = Color(prefs.getInt('primaryColor') ?? Colors.deepPurple.value);
      accentColor = Color(prefs.getInt('accentColor') ?? Colors.amber.value);
      darkMode = prefs.getBool('darkMode') ?? false;
    });
  }

  Future<void> savePrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fontFamily', fontFamily);
    await prefs.setDouble('fontSize', fontSize);
    await prefs.setInt('primaryColor', primaryColor.value);
    await prefs.setInt('accentColor', accentColor.value);
    await prefs.setBool('darkMode', darkMode);
  }

  void changeLanguage(String lang) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', lang);
    Locale newLocale = lang == 'ar' ? const Locale('ar', 'SA') : const Locale('en', 'US');
    MyApp.setLocale(context, newLocale);
    setState(() {
      selectedLanguage = lang;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إعدادات الذكاء الاصطناعي')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('اختر النموذج المستخدم:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButton<ModelType>(
              value: selected,
              items: [
                DropdownMenuItem(value: ModelType.lstm, child: Text('LSTM')),
                DropdownMenuItem(value: ModelType.cnn, child: Text('CNN')),
                DropdownMenuItem(value: ModelType.transformer, child: Text('Transformer-XL')),
                DropdownMenuItem(value: ModelType.rl, child: Text('Reinforcement Learning')),
                DropdownMenuItem(value: ModelType.automl, child: Text('AutoML (TPOT)')),
              ],
              onChanged: (v) {
                if (v != null) {
                  setState(() {
                    selected = v;
                    AIModelManager.selectModel(v);
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل AutoML (اختيار النموذج الأفضل تلقائيًا)'),
              value: autoML,
              onChanged: (v) {
                setState(() {
                  autoML = v;
                  AIModelManager.setAutoML(v);
                  if (v) selected = ModelType.automl;
                });
              },
            ),
            const SizedBox(height: 24),
            const Text('شرح:', style: TextStyle(fontWeight: FontWeight.bold)),
            const Text('يمكنك اختيار نوع النموذج الذكي المستخدم في التوصيات، أو تفعيل AutoML ليتم اختيار النموذج الأفضل تلقائيًا بناءً على البيانات.'),
            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('تغيير اللغة'),
              trailing: DropdownButton<String>(
                value: selectedLanguage,
                items: const [
                  DropdownMenuItem(value: 'ar', child: Text('العربية')),
                  DropdownMenuItem(value: 'en', child: Text('English')),
                ],
                onChanged: (v) {
                  if (v != null) changeLanguage(v);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ColorBox extends StatelessWidget {
  final Color color;
  final VoidCallback onTap;
  const ColorBox({required this.color, required this.onTap, super.key});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey),
        ),
      ),
    );
  }
}

Future<Color?> pickColor(BuildContext context, Color current) async {
  Color selected = current;
  return showDialog<Color>(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('اختر اللون'),
      content: SingleChildScrollView(
        child: ColorPicker(
          pickerColor: current,
          onColorChanged: (c) => selected = c,
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.pop(context, selected), child: const Text('تم')),
      ],
    ),
  );
} 