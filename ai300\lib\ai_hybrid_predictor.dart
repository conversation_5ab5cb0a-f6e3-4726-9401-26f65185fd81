import 'dart:typed_data';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:pytorch_mobile/pytorch_mobile.dart';

class AIHybridPredictor {
  static Interpreter? _lstmInterpreter;
  static Model? _cnnModel;
  static Model? _transformerModel;

  static Future<void> loadModels() async {
    _lstmInterpreter = await Interpreter.fromAsset('lstm_model.tflite');
    _cnnModel = await PyTorchMobile.loadModel('cnn_model.pt');
    _transformerModel = await PyTorchMobile.loadModel('transformerxl_model.pt');
  }

  static Future<double?> predictLSTM(List<double> input) async {
    if (_lstmInterpreter == null) await loadModels();
    var inputTensor = List.generate(1, (_) => List.generate(input.length, (i) => [input[i]]));
    var output = List.filled(1 * 1, 0.0).reshape([1, 1]);
    _lstmInterpreter!.run(inputTensor, output);
    return output[0][0];
  }

  static Future<double?> predictCNN(List<double> input) async {
    if (_cnnModel == null) await loadModels();
    var inputTensor = Float32List.fromList(input);
    var output = await _cnnModel!.getImagePrediction(inputTensor);
    return output is double ? output : null;
  }

  static Future<double?> predictTransformer(List<double> input) async {
    if (_transformerModel == null) await loadModels();
    var inputTensor = Float32List.fromList(input);
    var output = await _transformerModel!.getImagePrediction(inputTensor);
    return output is double ? output : null;
  }
} 