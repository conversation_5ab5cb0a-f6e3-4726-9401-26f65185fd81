from tpot import TPOTRegressor
from sklearn.model_selection import train_test_split
import joblib
import numpy as np
import os

def train_automl(X, y, generations=5, population_size=20):
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)
    tpot = TPOTRegressor(generations=generations, population_size=population_size, verbosity=2)
    tpot.fit(X_train, y_train)
    print(f'Test score: {tpot.score(X_test, y_test)}')
    ASSETS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib', 'assets', 'models')
    os.makedirs(ASSETS_DIR, exist_ok=True)
    joblib.dump(tpot.fitted_pipeline_, os.path.join(ASSETS_DIR, 'automl_model.pkl'))
    print(f'Best AutoML model exported to {os.path.join(ASSETS_DIR, "automl_model.pkl")}')

if __name__ == '__main__':
    X = np.random.randn(100, 30)
    y = np.random.randn(100)
    train_automl(X, y) 