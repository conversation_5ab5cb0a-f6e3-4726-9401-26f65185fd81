import 'package:flutter/material.dart';
import '../data/alternative_data_service.dart';

class AlternativeDataScreen extends StatefulWidget {
  const AlternativeDataScreen({super.key});
  @override
  State<AlternativeDataScreen> createState() => _AlternativeDataScreenState();
}

class _AlternativeDataScreenState extends State<AlternativeDataScreen> {
  Map<String, dynamic>? iotData;
  Map<String, dynamic>? satelliteData;
  bool loading = false;
  String error = '';

  Future<void> fetchData() async {
    setState(() { loading = true; error = ''; });
    try {
      final iot = await AlternativeDataService.fetchIoTData('AAPL');
      final sat = await AlternativeDataService.fetchSatelliteData('MENA');
      setState(() {
        iotData = iot;
        satelliteData = sat;
        loading = false;
      });
    } catch (e) {
      setState(() { error = e.toString(); loading = false; });
    }
  }

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('البيانات البديلة (IoT & أقمار صناعية)')),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : error.isNotEmpty
              ? Center(child: Text(error, style: const TextStyle(color: Colors.red)))
              : ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    Card(
                      child: ListTile(
                        leading: const Icon(Icons.sensors),
                        title: const Text('بيانات IoT (الطقس)'),
                        subtitle: Text(iotData != null ? iotData.toString() : 'لا توجد بيانات'),
                        trailing: IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: fetchData,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: ListTile(
                        leading: const Icon(Icons.satellite_alt),
                        title: const Text('بيانات أقمار صناعية (NDVI)'),
                        subtitle: satelliteData != null && satelliteData!['imageUrl'] != null
                            ? Image.network(satelliteData!['imageUrl'], height: 100)
                            : const Text('لا توجد بيانات'),
                        trailing: IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: fetchData,
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
} 