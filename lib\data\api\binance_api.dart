import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/candle_data.dart';

class BinanceApi {
  final String baseUrl = 'https://api.binance.com/api/v3';
  
  Future<List<CandleData>> getKlines({
    required String symbol,
    required String interval,
    int? limit,
    int? startTime,
    int? endTime,
  }) async {
    final Map<String, String> params = {
      'symbol': symbol,
      'interval': interval,
    };
    
    if (limit != null) params['limit'] = limit.toString();
    if (startTime != null) params['startTime'] = startTime.toString();
    if (endTime != null) params['endTime'] = endTime.toString();
    
    final Uri uri = Uri.parse('$baseUrl/klines').replace(queryParameters: params);
    final response = await http.get(uri);
    
    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((item) => CandleData.fromBinanceJson(item)).toList();
    } else {
      throw Exception('Failed to load klines: ${response.body}');
    }
  }
  
  Future<Map<String, dynamic>> getTicker(String symbol) async {
    final Uri uri = Uri.parse('$baseUrl/ticker/24hr').replace(
      queryParameters: {'symbol': symbol},
    );
    
    final response = await http.get(uri);
    
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load ticker: ${response.body}');
    }
  }
}