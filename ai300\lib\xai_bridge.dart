import 'dart:convert';
import 'package:http/http.dart' as http;

class XAIBridge {
  static Future<List<double>?> getShapImportances(List<double> closes, {String model = 'lstm'}) async {
    final url = Uri.parse('http://localhost:5005/xai_shap');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'close': closes, 'model': model}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['importances'] as List).map((e) => (e as num).toDouble()).toList();
    } else {
      return null;
    }
  }
} 