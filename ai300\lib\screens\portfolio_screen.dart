import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../risk_bridge.dart';

class PortfolioScreen extends StatefulWidget {
  const PortfolioScreen({super.key});
  @override
  State<PortfolioScreen> createState() => _PortfolioScreenState();
}

class _PortfolioScreenState extends State<PortfolioScreen> {
  Map<String, dynamic>? riskReport;
  List<List<double>>? monteCarloSim;
  bool loadingRisk = false;

  @override
  void initState() {
    super.initState();
    fetchRiskReport();
  }

  Future<void> fetchRiskReport() async {
    setState(() { loadingRisk = true; });
    // بيانات عوائد وهمية (استبدلها ببيانات حقيقية من المحفظة)
    List<double> returns = List.generate(100, (i) => 0.001 * (i % 5 - 2));
    riskReport = await RiskBridge.getRiskMetrics(returns);
    monteCarloSim = await RiskBridge.monteCarlo(start: 10000, mu: 0.001, sigma: 0.01, steps: 100, nSim: 100);
    setState(() { loadingRisk = false; });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المحفظة')),
      body: loadingRisk
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (riskReport != null) ...[
                    const Text('تقارير المخاطر:', style: TextStyle(fontWeight: FontWeight.bold)),
                    Text('Sharpe Ratio: ${riskReport!['sharpe']?.toStringAsFixed(3) ?? '-'}'),
                    Text('Sortino Ratio: ${riskReport!['sortino']?.toStringAsFixed(3) ?? '-'}'),
                    Text('Calmar Ratio: ${riskReport!['calmar']?.toStringAsFixed(3) ?? '-'}'),
                  ],
                  if (monteCarloSim != null) ...[
                    const SizedBox(height: 12),
                    const Text('محاكاة Monte Carlo (100 تجربة):', style: TextStyle(fontWeight: FontWeight.bold)),
                    SizedBox(
                      height: 180,
                      child: ListView(
                        scrollDirection: Axis.horizontal,
                        children: [
                          for (final sim in monteCarloSim!.take(10))
                            Container(
                              width: 120,
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(border: Border.all(color: Colors.blueGrey)),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text('نهاية المحاكاة: ${sim.last.toStringAsFixed(2)}'),
                                  const SizedBox(height: 4),
                                  Expanded(
                                    child: CustomPaint(
                                      painter: _MonteCarloPainter(sim),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}

class _MonteCarloPainter extends CustomPainter {
  final List<double> sim;
  _MonteCarloPainter(this.sim);
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 1.5;
    double minY = sim.reduce((a, b) => a < b ? a : b);
    double maxY = sim.reduce((a, b) => a > b ? a : b);
    for (int i = 1; i < sim.length; i++) {
      double x1 = (i - 1) / (sim.length - 1) * size.width;
      double y1 = size.height - ((sim[i - 1] - minY) / (maxY - minY + 1e-6) * size.height);
      double x2 = i / (sim.length - 1) * size.width;
      double y2 = size.height - ((sim[i] - minY) / (maxY - minY + 1e-6) * size.height);
      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), paint);
    }
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 