import 'package:flutter_test/flutter_test.dart';
import 'package:ai_300/models/data_provider.dart';

void main() {
  group('DataIntegration', () {
    test('Alpha Vantage integration', () async {
      final data = await DataProvider.getAlphaVantageOHLCV(symbol: 'AAPL');
      expect(data, isA<List>());
    });
    test('CoinGecko integration', () async {
      final data = await DataProvider.getCoinGeckoOHLCV(coinId: 'bitcoin');
      expect(data, isA<List>());
    });
    test('OANDA integration', () async {
      final data = await DataProvider.getOandaOHLCV(instrument: 'EUR_USD');
      expect(data, isA<List>());
    });
    test('Finnhub integration', () async {
      final data = await DataProvider.getFinnhubOHLCV(symbol: 'AAPL');
      expect(data, isA<List>());
    });
    test('StockTwits integration', () async {
      final data = await DataProvider.getStockTwitsNews(symbol: 'AAPL');
      expect(data, isA<List>());
    });
    test('Economic Calendar integration', () async {
      final data = await DataProvider.getEconomicEvents(country: 'us');
      expect(data, isA<List>());
    });
    test('Cache works (Alpha Vantage)', () async {
      final data1 = await DataProvider.getAlphaVantageOHLCV(symbol: 'AAPL');
      final data2 = await DataProvider.getAlphaVantageOHLCV(symbol: 'AAPL');
      expect(data1, equals(data2));
    });
    // اختبار WebSocket (mock)
    test('Binance WebSocket (mock)', () async {
      // لاختبار حقيقي: استمع للستريم وتحقق من وصول بيانات
      expect(true, true);
    });
  });
} 