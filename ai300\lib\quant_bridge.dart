import 'dart:convert';
import 'package:http/http.dart' as http;

class QuantBridge {
  static Future<Map<String, dynamic>?> runBacktest({
    required String strategyCode,
    required String dataCsv,
    required String start,
    required String end,
    int capitalBase = 10000,
  }) async {
    final url = Uri.parse('http://localhost:5003/backtest');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'strategy_code': strategyCode,
        'data_csv': dataCsv,
        'start': start,
        'end': end,
        'capital_base': capitalBase,
      }),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }
} 