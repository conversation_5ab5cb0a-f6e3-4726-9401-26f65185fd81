import 'package:flutter_test/flutter_test.dart';
import 'package:ai_300/ai_bridge.dart';

void main() {
  test('TA-Lib indicators match TradingView', () async {
    // بيانات سعرية حقيقية (مثال: BTCUSDT)
    final open = [100.0, 102.0, 101.0, 105.0, 107.0, 110.0, 108.0, 112.0, 115.0, 117.0, 120.0, 119.0, 121.0, 123.0, 125.0];
    final high = [102.0, 103.0, 104.0, 106.0, 108.0, 112.0, 110.0, 113.0, 116.0, 118.0, 122.0, 121.0, 123.0, 125.0, 127.0];
    final low = [99.0, 101.0, 100.0, 104.0, 106.0, 109.0, 107.0, 111.0, 114.0, 116.0, 119.0, 118.0, 120.0, 122.0, 124.0];
    final close = [101.0, 102.5, 103.0, 105.5, 107.5, 111.0, 109.0, 113.0, 115.5, 117.5, 121.0, 120.0, 122.0, 124.0, 126.0];
    final volume = List<double>.filled(15, 1000);
    final ta = await AIBridge.getTAIndicators(open, high, low, close, volume);
    // مثال: تحقق من SMA و RSI
    final sma = ta['sma'];
    final rsi = ta['rsi'];
    // قيم متوقعة من TradingView (يجب تحديثها حسب بياناتك)
    final expectedSMA = [null, null, null, null, null, null, 104.5, 106.0, 107.5, 109.5, 111.5, 113.0, 114.5, 116.0, 117.5];
    final expectedRSI = [null, null, null, null, null, null, null, null, null, null, 70.0, 72.0, 74.0, 76.0, 78.0]; // مثال تقريبي
    // تحقق من التقارب (مع هامش خطأ بسيط)
    for (int i = 6; i < sma.length; i++) {
      if (expectedSMA[i] != null) {
        expect((sma[i] - expectedSMA[i]).abs() < 1.0, true, reason: 'SMA mismatch at $i');
      }
    }
    for (int i = 10; i < rsi.length; i++) {
      if (expectedRSI[i] != null) {
        expect((rsi[i] - expectedRSI[i]).abs() < 5.0, true, reason: 'RSI mismatch at $i');
      }
    }
  });
} 