import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class WearService {
  static Future<void> sendWearNotification(String title, String body) async {
    const androidDetails = AndroidNotificationDetails(
      'wear_channel',
      'Wear Notifications',
      importance: Importance.high,
      priority: Priority.high,
      styleInformation: BigTextStyleInformation(''),
    );
    const notificationDetails = NotificationDetails(android: androidDetails);
    await FlutterLocalNotificationsPlugin().show(
      1,
      title,
      body,
      notificationDetails,
    );
  }
} 