import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../main.dart';
import '../analysis.dart';
import '../ai_models.dart';
import 'dart:math';
import '../ai_bridge.dart';
import 'package:share_plus/share_plus.dart';
import 'voice_command.dart';
import '../wear_service.dart';
import '../screens/ar_screen.dart';
import '../screens/charts_screen.dart';
import '../screens/analysis_screen.dart';
import '../screens/trading_screen.dart';
import '../screens/portfolio_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/help_screen.dart';
import '../screens/logout_screen.dart';
import 'package:reorderables/reorderables.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'voice_command_widget.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFF181C1F),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(Icons.dashboard, color: Colors.amber[700], size: 32),
                const SizedBox(width: 12),
                Text('لوحة التحكم', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.amber[700], letterSpacing: 1.2)),
              ],
            ),
          ),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              padding: const EdgeInsets.all(16),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _DashboardCard(
                  title: 'الرصيد الكلي',
                  value: '120,000 USD',
                  icon: Icons.account_balance_wallet,
                  color: Colors.blueGrey[900],
                  valueColor: Colors.greenAccent,
                ),
                _DashboardCard(
                  title: 'الأصول',
                  value: 'BTC, ETH, AAPL',
                  icon: Icons.pie_chart,
                  color: Colors.blueGrey[900],
                  valueColor: Colors.amber,
                ),
                _DashboardCard(
                  title: 'تنبيهات',
                  value: '3 إشعارات',
                  icon: Icons.notifications_active,
                  color: Colors.blueGrey[900],
                  valueColor: Colors.redAccent,
                ),
                _DashboardCard(
                  title: 'توصية AI',
                  value: 'شراء BTC',
                  icon: Icons.trending_up,
                  color: Colors.blueGrey[900],
                  valueColor: Colors.green,
                ),
              ],
            ),
          ),
          Container(
            color: Colors.black,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.access_time, color: Colors.grey[400], size: 18),
                const SizedBox(width: 6),
                Text('Market Open', style: TextStyle(color: Colors.greenAccent[400], fontWeight: FontWeight.bold)),
                const Spacer(),
                Icon(Icons.network_check, color: Colors.amber[700], size: 18),
                const SizedBox(width: 6),
                Text('Realtime Data', style: TextStyle(color: Colors.amber[700], fontWeight: FontWeight.bold)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _DashboardCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final Color? valueColor;
  const _DashboardCard({required this.title, required this.value, required this.icon, this.color, this.valueColor});
  @override
  Widget build(BuildContext context) {
    return Card(
      color: color ?? Colors.blueGrey[900],
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: valueColor, size: 32),
            const SizedBox(height: 12),
            Text(title, style: const TextStyle(color: Colors.white70, fontWeight: FontWeight.bold, fontSize: 16)),
            const SizedBox(height: 8),
            Text(value, style: TextStyle(color: valueColor, fontWeight: FontWeight.bold, fontSize: 20)),
          ],
        ),
      ),
    );
  }
}

class _DashboardScreenState extends State<DashboardScreen> {
  Map<String, double?> prices = {
    'BTCUSDT': null,
    'ETHUSDT': null,
    'BNBUSDT': null,
  };
  bool loading = true;
  String? error;
  Map<String, dynamic>? yahooData;
  String? aiRecommendation;
  double? simpleReturn;
  double? sharpeRatio;
  double? lstmPrediction;
  double? lstmConfidence;
  String? detectedPattern;
  List<String> _panelOrder = [
    'balance',
    'chart',
    'performance',
    'recommendations',
  ];

  final Map<String, Widget> _panels = {
    'balance': Card(child: ListTile(title: Text('الرصيد'))),
    'chart': Card(child: ListTile(title: Text('الرسم البياني'))),
    'performance': Card(child: ListTile(title: Text('الأداء'))),
    'recommendations': Card(child: ListTile(title: Text('التوصيات'))),
  };

  @override
  void initState() {
    super.initState();
    fetchAllPrices();
    fetchYahoo();
    _loadOrder();
  }

  Future<void> fetchAllPrices() async {
    setState(() {
      loading = true;
      error = null;
    });
    try {
      final symbols = prices.keys.toList();
      final results = await Future.wait(symbols.map((s) => fetchMarketData(symbol: s)));
      setState(() {
        for (int i = 0; i < symbols.length; i++) {
          prices[symbols[i]] = double.tryParse(results[i]['price'].toString());
        }
        final closes = results.map((e) => double.tryParse(e['price'].toString()) ?? 0.0).toList();
        aiRecommendation = SimpleAIPredictor.predictDirection(closes);
        detectedPattern = await AIBridge.detectPattern(closes);
        // توقع LSTM
        () async {
          lstmPrediction = await AIBridge.predictLSTM(closes);
          // حساب نسبة الثقة بناءً على الانحراف المعياري
          double mean = closes.reduce((a, b) => a + b) / closes.length;
          double std = closes.length > 1 ? sqrt(closes.map((c) => pow(c - mean, 2)).reduce((a, b) => a + b) / closes.length) : 0;
          lstmConfidence = mean != 0 ? (1 - (std / mean)).clamp(0, 1) : null;
          setState(() {});
        }();
        // حساب العائد البسيط وSharpe Ratio
        if (closes.length > 1) {
          simpleReturn = QuantitativeAnalysis.simpleReturn(closes.first, closes.last);
          double mean = closes.reduce((a, b) => a + b) / closes.length;
          double std = sqrt(closes.map((c) => pow(c - mean, 2)).reduce((a, b) => a + b) / closes.length);
          sharpeRatio = std != 0 ? (mean / std) : null;
        }
        loading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        loading = false;
      });
    }
  }

  Future<void> fetchYahoo() async {
    final data = await FundamentalAnalysis.fetchYahooFinance('BTC-USD');
    setState(() {
      yahooData = data;
    });
  }

  Future<void> _loadOrder() async {
    final prefs = await SharedPreferences.getInstance();
    final saved = prefs.getString('dashboard_panel_order');
    if (saved != null) {
      setState(() {
        _panelOrder = List<String>.from(jsonDecode(saved));
      });
    }
  }

  Future<void> _saveOrder() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('dashboard_panel_order', jsonEncode(_panelOrder));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('لوحة التحكم')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            VoiceCommandWidget(
              onCommand: (cmd) {
                if (cmd == 'trade') {
                  // تنفيذ صفقة وهمية
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تنفيذ صفقة!')));
                } else if (cmd == 'charts') {
                  Navigator.of(context).push(MaterialPageRoute(builder: (_) => const ChartsScreen()));
                } else if (cmd == 'dashboard') {
                  // إعادة تحميل لوحة التحكم
                  fetchAllPrices();
                }
              },
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ReorderableWrap(
                spacing: 16,
                runSpacing: 16,
                onReorder: (oldIndex, newIndex) {
                  setState(() {
                    final item = _panelOrder.removeAt(oldIndex);
                    _panelOrder.insert(newIndex, item);
                  });
                  _saveOrder();
                },
                children: _panelOrder.map((key) => _panels[key]!).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showNotification(String title, String body) async {
    const androidDetails = AndroidNotificationDetails(
      'trade_channel',
      'Trade Notifications',
      importance: Importance.high,
      priority: Priority.high,
    );
    const notificationDetails = NotificationDetails(android: androidDetails);
    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      notificationDetails,
    );
  }
}

class CandlestickChart extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: LineChart(
        LineChartData(
          titlesData: FlTitlesData(show: true),
          borderData: FlBorderData(show: true),
          lineBarsData: [
            LineChartBarData(
              spots: [
                FlSpot(0, 100),
                FlSpot(1, 105),
                FlSpot(2, 102),
                FlSpot(3, 110),
                FlSpot(4, 108),
              ],
              isCurved: true,
              color: Colors.green,
              dotData: FlDotData(show: false),
            ),
          ],
        ),
      ),
    );
  }
} 