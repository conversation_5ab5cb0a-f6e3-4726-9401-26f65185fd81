import 'dart:convert';
import 'package:http/http.dart' as http;

class RiskBridge {
  static Future<Map<String, dynamic>?> getRiskMetrics(List<double> returns, {double riskFree = 0.0}) async {
    final url = Uri.parse('http://localhost:5008/risk_metrics');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'returns': returns, 'risk_free': riskFree}),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return null;
    }
  }

  static Future<List<List<double>>?> monteCarlo({
    required double start,
    required double mu,
    required double sigma,
    required int steps,
    int nSim = 1000,
  }) async {
    final url = Uri.parse('http://localhost:5008/monte_carlo');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'start': start, 'mu': mu, 'sigma': sigma, 'steps': steps, 'n_sim': nSim}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['simulations'] as List).map((e) => (e as List).map((v) => (v as num).toDouble()).toList()).toList();
    } else {
      return null;
    }
  }
} 