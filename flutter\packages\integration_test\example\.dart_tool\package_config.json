{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-80.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "analyzer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-7.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "coverage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/coverage-1.11.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter", "rootUri": "file:///D:/ai%20300/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_driver", "rootUri": "file:///D:/ai%20300/flutter/packages/flutter_driver", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_test", "rootUri": "file:///D:/ai%20300/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "frontend_server_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fuchsia_remote_debug_protocol", "rootUri": "file:///D:/ai%20300/flutter/packages/fuchsia_remote_debug_protocol", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "glob", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "http_multi_server", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "integration_test", "rootUri": "file:///D:/ai%20300/flutter/packages/integration_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "integration_test_macos", "rootUri": "../../integration_test_macos", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "io", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "node_preamble", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "package_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pedantic", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pedantic-1.11.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pool", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "process", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/process-5.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "pub_semver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_packages_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_static-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf_web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-2.0.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///D:/ai%20300/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_map_stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_map_stack_trace-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_maps", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_maps-0.10.13", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sync_http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sync_http-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test-1.25.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_core-0.6.8", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web_socket_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webdriver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/webdriver-3.1.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "integration_test_example", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generator": "pub", "generatorVersion": "3.8.0", "flutterRoot": "file:///D:/ai%20300/flutter", "flutterVersion": "3.32.0", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}