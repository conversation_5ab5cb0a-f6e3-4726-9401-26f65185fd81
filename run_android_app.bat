@echo off
echo Starting Signal Black Super Trading App...
echo.

REM Change to app directory
cd /d "D:\ai 300\android_trading_app"

REM Set Flutter path
set FLUTTER_PATH="D:\ai 300\flutter\bin\flutter.bat"

echo Step 1: Getting dependencies...
%FLUTTER_PATH% pub get

echo.
echo Step 2: Starting emulator...
start /min %FLUTTER_PATH% emulators --launch Medium_Phone_API_36.0

echo.
echo Step 3: Waiting for emulator to start...
timeout /t 60 /nobreak

echo.
echo Step 4: Running app on Android...
%FLUTTER_PATH% run

echo.
echo If Android emulator failed, trying web version...
%FLUTTER_PATH% run -d chrome --web-port 8080

pause
