import 'package:flutter_test/flutter_test.dart';
import 'package:ai_300/security/security_service.dart';
import 'dart:io';

void main() {
  group('SecurityService', () {
    test('OTP generation and verification', () {
      final otp = SecurityService.generateOTP('1234567890');
      expect(otp.length, 6);
      expect(SecurityService.verifyOTP(otp, otp), true);
      expect(SecurityService.verifyOTP('000000', otp), false);
    });

    test('TOTP generation and verification', () {
      final secret = 'JBSWY3DPEHPK3PXP';
      final code = SecurityService.generateTOTP(secret);
      expect(code.length, greaterThanOrEqualTo(6));
      expect(SecurityService.verifyTOTP(secret, code), true);
    });

    test('Text encryption and decryption', () {
      final plain = 'my secret data';
      final encrypted = SecurityService.encryptText(plain);
      final decrypted = SecurityService.decryptText(encrypted);
      expect(decrypted, plain);
    });

    test('File encryption and decryption (mock)', () async {
      final file = File('test.txt');
      await file.writeAsString('file secret');
      await SecurityService.encryptFile('test.txt', 'test.enc');
      await SecurityService.decryptFile('test.enc', 'test2.txt');
      final content = await File('test2.txt').readAsString();
      expect(content, 'file secret');
      await file.delete();
      await File('test.enc').delete();
      await File('test2.txt').delete();
    });
  });
} 