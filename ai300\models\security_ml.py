import numpy as np
from sklearn.ensemble import IsolationForest
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
import os
import time
import requests
import logging
import sys
import subprocess
import psutil
# Sentry
import sentry_sdk

# إعداد Sentry (ضع DSN الخاص بك هنا)
sentry_sdk.init(dsn="YOUR_SENTRY_DSN", traces_sample_rate=1.0)

# إعداد logging متقدم مع دعم Datadog وELK
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler('security_ml.log'),
        logging.FileHandler('datadog.log'),  # ملف خاص لوكيل Datadog
        logging.FileHandler('elk.log'),      # ملف خاص لـ Filebeat/ELK
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# إشعار عبر البريد (واجهة)
def send_email_alert(subject, body, to_email):
    # استخدم SMTP أو API مثل SendGrid
    logger.warning(f'Email alert: {subject} -> {to_email}')
    # مثال: requests.post('https://api.sendgrid.com/v3/mail/send', ...)

# إشعار عبر Slack (واجهة)
def send_slack_alert(message, webhook_url):
    try:
        requests.post(webhook_url, json={"text": message})
        logger.warning(f'Slack alert sent: {message}')
    except Exception as e:
        logger.error(f'Slack alert error: {e}')

# مراقبة مؤشرات الأداء
PERF_LOG_INTERVAL = 60  # ثانية
def log_performance():
    cpu = psutil.cpu_percent()
    mem = psutil.virtual_memory().percent
    logger.info(f'PERF: CPU={cpu}%, MEM={mem}%')
    return cpu, mem

def log_latency(start_time, label='latency'):
    latency = time.time() - start_time
    logger.info(f'{label}: {latency:.3f}s')
    return latency

# Hyperledger Fabric REST API (واجهة مبسطة)
FABRIC_API_URL = 'http://localhost:4000/api/transactions'  # مثال

# كشف الشذوذ
request_times = {}
def detect_anomaly(request_features, model=None, ip=None, max_per_minute=60):
    try:
        if model is None:
            model = IsolationForest(contamination=0.01, random_state=42)
            X_train = np.random.normal(0, 1, (1000, len(request_features)))
            model.fit(X_train)
        pred = model.predict([request_features])[0]
        if ip:
            now = int(time.time() // 60)
            if ip not in request_times:
                request_times[ip] = {}
            if now not in request_times[ip]:
                request_times[ip][now] = 0
            request_times[ip][now] += 1
            if request_times[ip][now] > max_per_minute:
                logger.warning(f'Rate limit exceeded for IP {ip}')
                send_slack_alert(f'Rate limit exceeded for IP {ip}', 'YOUR_SLACK_WEBHOOK_URL')
                send_email_alert('Rate limit exceeded', f'IP: {ip}', '<EMAIL>')
                sentry_sdk.capture_message(f'Rate limit exceeded for IP {ip}', level='warning')
                return True
        return pred == -1
    except Exception as e:
        logger.error(f'Anomaly detection error: {e}')
        sentry_sdk.capture_exception(e)
        send_email_alert('Anomaly detection error', str(e), '<EMAIL>')
        return True

def encrypt_data(plain, key):
    try:
        backend = default_backend()
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=backend)
        encryptor = cipher.encryptor()
        ct = encryptor.update(plain.encode()) + encryptor.finalize()
        return base64.b64encode(iv + ct).decode()
    except Exception as e:
        logger.error(f'Encryption error: {e}')
        sentry_sdk.capture_exception(e)
        send_email_alert('Encryption error', str(e), '<EMAIL>')
        return None

def decrypt_data(enc, key):
    try:
        backend = default_backend()
        data = base64.b64decode(enc)
        iv, ct = data[:16], data[16:]
        cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=backend)
        decryptor = cipher.decryptor()
        pt = decryptor.update(ct) + decryptor.finalize()
        return pt.decode()
    except Exception as e:
        logger.error(f'Decryption error: {e}')
        sentry_sdk.capture_exception(e)
        send_email_alert('Decryption error', str(e), '<EMAIL>')
        return None

def encrypt_file(input_path, output_path, key):
    try:
        backend = default_backend()
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=backend)
        encryptor = cipher.encryptor()
        with open(input_path, 'rb') as f:
            data = f.read()
        ct = encryptor.update(data) + encryptor.finalize()
        with open(output_path, 'wb') as f:
            f.write(iv + ct)
        logger.info(f'File encrypted: {output_path}')
    except Exception as e:
        logger.error(f'File encryption error: {e}')
        sentry_sdk.capture_exception(e)
        send_email_alert('File encryption error', str(e), '<EMAIL>')

def decrypt_file(input_path, output_path, key):
    try:
        backend = default_backend()
        with open(input_path, 'rb') as f:
            data = f.read()
        iv, ct = data[:16], data[16:]
        cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=backend)
        decryptor = cipher.decryptor()
        pt = decryptor.update(ct) + decryptor.finalize()
        with open(output_path, 'wb') as f:
            f.write(pt)
        logger.info(f'File decrypted: {output_path}')
    except Exception as e:
        logger.error(f'File decryption error: {e}')
        sentry_sdk.capture_exception(e)
        send_email_alert('File decryption error', str(e), '<EMAIL>')

def sign_transaction_fabric(tx_data, user_token):
    try:
        headers = {'Authorization': f'Bearer {user_token}', 'Content-Type': 'application/json'}
        resp = requests.post(FABRIC_API_URL, json=tx_data, headers=headers)
        if resp.status_code == 200:
            logger.info('Transaction signed on Fabric')
            return resp.json().get('signature')
        else:
            logger.error(f'Fabric sign error: {resp.text}')
            sentry_sdk.capture_message(f'Fabric sign error: {resp.text}', level='error')
            send_email_alert('Fabric sign error', resp.text, '<EMAIL>')
            return None
    except Exception as e:
        logger.error(f'Fabric sign exception: {e}')
        sentry_sdk.capture_exception(e)
        send_email_alert('Fabric sign exception', str(e), '<EMAIL>')
        return None

# مراقبة الخدمة وإعادة التشغيل التلقائي
WATCHDOG_CMD = [sys.executable, sys.argv[0]]
def watchdog_main():
    while True:
        try:
            logger.info('Starting main service...')
            perf_start = time.time()
            exit_code = subprocess.call(WATCHDOG_CMD)
            log_latency(perf_start, label='service_runtime')
            if exit_code == 0:
                logger.info('Service exited normally.')
                break
            else:
                logger.warning(f'Service crashed with code {exit_code}, restarting...')
                sentry_sdk.capture_message(f'Service crashed with code {exit_code}', level='error')
        except Exception as e:
            logger.error(f'Watchdog error: {e}')
            sentry_sdk.capture_exception(e)
        time.sleep(2)

# ملاحظات تفعيل Watchdog خارجي:
# - systemd: استخدم Restart=always في ملف الخدمة
# - supervisor: استخدم autorestart=true
# - Docker: استخدم healthcheck و restart: always في docker-compose

if __name__ == '__main__':
    # مثال: مراقبة الخدمة
    # watchdog_main()
    # تسجيل مؤشرات الأداء كل دقيقة
    # while True:
    #     log_performance()
    #     time.sleep(PERF_LOG_INTERVAL)
    # كشف شذوذ ومعدل طلبات
    print('Anomaly:', detect_anomaly([0.5, -0.2, 0.1], ip='*******'))
    # تشفير/فك تشفير نص
    key = b'my32lengthsupersecretnooneknows1'
    enc = encrypt_data('secret data', key)
    print('Encrypted:', enc)
    print('Decrypted:', decrypt_data(enc, key))
    # تشفير/فك تشفير ملف
    # encrypt_file('plain.txt', 'enc.bin', key)
    # decrypt_file('enc.bin', 'plain2.txt', key)
    # توقيع رقمي عبر Fabric
    # print('Fabric Signature:', sign_transaction_fabric({'order': 123}, 'user_token_here')) 