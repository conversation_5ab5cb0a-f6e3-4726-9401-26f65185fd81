from flask import Flask, request, jsonify
from flask_cors import CORS
import talib
import numpy as np

app = Flask(__name__)
CORS(app)

@app.route('/ta', methods=['POST'])
def ta_indicators():
    data = request.json
    close = np.array(data['close'], dtype=float)
    high = np.array(data.get('high', close), dtype=float)
    low = np.array(data.get('low', close), dtype=float)
    open_ = np.array(data.get('open', close), dtype=float)
    volume = np.array(data.get('volume', [1000]*len(close)), dtype=float)
    result = {
        'sma': talib.SMA(close, timeperiod=7).tolist(),
        'ema': talib.EMA(close, timeperiod=14).tolist(),
        'rsi': talib.RSI(close, timeperiod=14).tolist(),
        'macd': talib.MACD(close)[0].tolist(),
        'bollinger_upper': talib.BBANDS(close)[0].tolist(),
        'bollinger_lower': talib.BBANDS(close)[2].tolist(),
        'stochastic_k': talib.STOCH(high, low, close)[0].tolist(),
        'atr': talib.ATR(high, low, close, timeperiod=14).tolist(),
        'obv': talib.OBV(close, volume).tolist(),
        'williams_r': talib.WILLR(high, low, close, timeperiod=14).tolist(),
        'vpt': talib.AD(high, low, close, volume).tolist(),
        'momentum': talib.MOM(close, timeperiod=10).tolist(),
        'demark': talib.MINUS_DI(high, low, close, timeperiod=14).tolist(),
        'ad': talib.AD(high, low, close, volume).tolist(),
        'ichimoku': {},
        'fibonacci': [],
        'parabolic_sar': talib.SAR(high, low).tolist(),
    }
    # Ichimoku (مبسط)
    if len(close) >= 26:
        result['ichimoku'] = {
            'tenkan_sen': np.mean(close[-9:]),
            'kijun_sen': np.mean(close[-26:]),
            'senkou_span_a': (np.mean(close[-9:]) + np.mean(close[-26:])) / 2,
            'senkou_span_b': np.mean(close[-52:]) if len(close) >= 52 else 0,
        }
    # Fibonacci (مبسط)
    if len(close) > 0:
        max_price = np.max(close)
        min_price = np.min(close)
        diff = max_price - min_price
        result['fibonacci'] = [max_price - diff * r for r in [0.236, 0.382, 0.5, 0.618, 0.786]]
    return jsonify(result)

if __name__ == '__main__':
    app.run(port=5001, debug=True) 