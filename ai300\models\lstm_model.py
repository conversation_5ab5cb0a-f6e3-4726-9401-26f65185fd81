import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import shap
import os

def build_lstm_model(input_shape):
    model = keras.Sequential([
        layers.LSTM(64, input_shape=input_shape, return_sequences=True),
        layers.LSTM(32),
        layers.Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    return model

def train_and_export(X_train, y_train, epochs=10):
    model = build_lstm_model((X_train.shape[1], X_train.shape[2]))
    model.fit(X_train, y_train, epochs=epochs, batch_size=32)
    # تصدير إلى TFLite مع دعم ops الخاصة
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    converter.target_spec.supported_ops = [tf.lite.OpsSet.TFLITE_BUILTINS, tf.lite.OpsSet.SELECT_TF_OPS]
    converter._experimental_lower_tensor_list_ops = False
    tflite_model = converter.convert()
    ASSETS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib', 'assets', 'models')
    os.makedirs(ASSETS_DIR, exist_ok=True)
    with open(os.path.join(ASSETS_DIR, 'lstm_model.tflite'), 'wb') as f:
        f.write(tflite_model)
    print(f'LSTM model exported to {os.path.join(ASSETS_DIR, "lstm_model.tflite")}')
    # شرح XAI باستخدام SHAP
    explainer = shap.DeepExplainer(model, X_train[:100])
    shap_values = explainer.shap_values(X_train[:10])
    np.save(os.path.join(ASSETS_DIR, 'lstm_shap_values.npy'), shap_values)
    print('SHAP values exported to', os.path.join(ASSETS_DIR, 'lstm_shap_values.npy'))

if __name__ == '__main__':
    X_train = np.random.randn(100, 30, 1).astype(np.float32)
    y_train = np.random.randn(100, 1).astype(np.float32)
    train_and_export(X_train, y_train) 