import 'dart:convert';
import 'package:http/http.dart' as http;

class AutoMLBridge {
  static Future<Map<String, dynamic>?> trainAutoML(List<List<double>> X, List<double> y) async {
    final url = Uri.parse('http://localhost:5006/automl_train');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'X': X, 'y': y}),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }

  static Future<List<double>?> predictAutoML(List<List<double>> X) async {
    final url = Uri.parse('http://localhost:5006/automl_predict');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'X': X}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['predictions'] as List).map((e) => (e as num).toDouble()).toList();
    } else {
      return null;
    }
  }

  static Future<bool> onlineLearn(Map<String, dynamic> x, double y) async {
    final url = Uri.parse('http://localhost:5006/online_learn');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'x': x, 'y': y}),
    );
    return response.statusCode == 200;
  }

  static Future<double?> onlinePredict(Map<String, dynamic> x) async {
    final url = Uri.parse('http://localhost:5006/online_predict');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'x': x}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['prediction'] as num).toDouble();
    } else {
      return null;
    }
  }
} 