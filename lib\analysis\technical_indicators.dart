import 'package:technical_indicators/technical_indicators.dart';
import '../data/models/candle_data.dart';

class TechnicalAnalysis {
  // Moving Averages
  static List<double?> calculateSMA(List<CandleData> candles, int period) {
    final List<double> closes = candles.map((c) => c.close).toList();
    return SMA.calculate(period: period, data: closes);
  }
  
  static List<double?> calculateEMA(List<CandleData> candles, int period) {
    final List<double> closes = candles.map((c) => c.close).toList();
    return EMA.calculate(period: period, data: closes);
  }
  
  // Oscillators
  static List<double?> calculateRSI(List<CandleData> candles, int period) {
    final List<double> closes = candles.map((c) => c.close).toList();
    return RSI.calculate(period: period, data: closes);
  }
  
  static Map<String, List<double?>> calculateMACD(
    List<CandleData> candles, {
    int fastPeriod = 12,
    int slowPeriod = 26,
    int signalPeriod = 9,
  }) {
    final List<double> closes = candles.map((c) => c.close).toList();
    final result = MACD.calculate(
      fastPeriod: fastPeriod,
      slowPeriod: slowPeriod,
      signalPeriod: signalPeriod,
      data: closes,
    );
    
    return {
      'macd': result.macd,
      'signal': result.signal,
      'histogram': result.histogram,
    };
  }
  
  // Volatility Indicators
  static Map<String, List<double?>> calculateBollingerBands(
    List<CandleData> candles, {
    int period = 20,
    double stdDev = 2.0,
  }) {
    final List<double> closes = candles.map((c) => c.close).toList();
    final result = BollingerBands.calculate(
      period: period,
      stdDev: stdDev,
      data: closes,
    );
    
    return {
      'upper': result.upper,
      'middle': result.middle,
      'lower': result.lower,
    };
  }
  
  static List<double?> calculateATR(List<CandleData> candles, int period) {
    final List<double> highs = candles.map((c) => c.high).toList();
    final List<double> lows = candles.map((c) => c.low).toList();
    final List<double> closes = candles.map((c) => c.close).toList();
    
    return ATR.calculate(
      period: period,
      high: highs,
      low: lows,
      close: closes,
    );
  }
  
  // Volume Indicators
  static List<double?> calculateOBV(List<CandleData> candles) {
    final List<double> closes = candles.map((c) => c.close).toList();
    final List<double> volumes = candles.map((c) => c.volume).toList();
    
    return OBV.calculate(close: closes, volume: volumes);
  }
}