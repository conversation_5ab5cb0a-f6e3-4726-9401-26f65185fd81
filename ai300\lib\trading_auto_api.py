from flask import Flask, request, jsonify
from flask_cors import CORS
import ccxt
import MetaTrader5 as mt5
from ib_insync import IB, Stock, util
import time
import math
import os
import json

app = Flask(__name__)
CORS(app)

ORDER_LOG = 'order_log.json'

# إدارة المخاطر

def fixed_fractional(balance, risk_per_trade):
    return balance * risk_per_trade

def kelly_criterion(win_rate, win_loss_ratio):
    return max((win_rate - (1 - win_rate) / win_loss_ratio), 0)

# سجل الأوامر

def log_order(order):
    if os.path.exists(ORDER_LOG):
        with open(ORDER_LOG, 'r') as f:
            orders = json.load(f)
    else:
        orders = []
    orders.append(order)
    with open(ORDER_LOG, 'w') as f:
        json.dump(orders, f)

@app.route('/trade_binance', methods=['POST'])
def trade_binance():
    data = request.json
    api_key = data['api_key']
    api_secret = data['api_secret']
    symbol = data['symbol']
    side = data['side']
    type_ = data['type']
    amount = float(data['amount'])
    price = float(data.get('price', 0))
    exchange = ccxt.binance({
        'apiKey': api_key,
        'secret': api_secret,
        'enableRateLimit': True
    })
    params = {}
    if type_ == 'market':
        order = exchange.create_market_order(symbol, side, amount)
    elif type_ == 'limit':
        order = exchange.create_limit_order(symbol, side, amount, price)
    elif type_ == 'stop':
        params['stopPrice'] = price
        order = exchange.create_order(symbol, type_='STOP_MARKET', side=side, amount=amount, params=params)
    else:
        return jsonify({'error': 'Invalid order type'}), 400
    log_order({'exchange': 'binance', 'order': order})
    return jsonify(order)

@app.route('/trade_mt5', methods=['POST'])
def trade_mt5():
    data = request.json
    symbol = data['symbol']
    side = data['side']
    type_ = data['type']
    amount = float(data['amount'])
    price = float(data.get('price', 0))
    login = int(data['login'])
    password = data['password']
    server = data['server']
    mt5.initialize(login=login, password=password, server=server)
    request_dict = {
        'action': mt5.TRADE_ACTION_DEAL,
        'symbol': symbol,
        'volume': amount,
        'type': mt5.ORDER_TYPE_BUY if side == 'buy' else mt5.ORDER_TYPE_SELL,
        'price': price if type_ != 'market' else mt5.symbol_info_tick(symbol).ask,
        'deviation': 10,
        'magic': 234000,
        'comment': 'AI300',
        'type_time': mt5.ORDER_TIME_GTC,
        'type_filling': mt5.ORDER_FILLING_IOC,
    }
    result = mt5.order_send(request_dict)
    mt5.shutdown()
    log_order({'exchange': 'mt5', 'order': result._asdict()})
    return jsonify(result._asdict())

@app.route('/trade_ib', methods=['POST'])
def trade_ib():
    data = request.json
    symbol = data['symbol']
    side = data['side']
    type_ = data['type']
    amount = float(data['amount'])
    price = float(data.get('price', 0))
    ib = IB()
    ib.connect('127.0.0.1', 7497, clientId=1)
    contract = Stock(symbol, 'SMART', 'USD')
    if type_ == 'market':
        order = ib.marketOrder('BUY' if side == 'buy' else 'SELL', amount)
    elif type_ == 'limit':
        order = ib.limitOrder('BUY' if side == 'buy' else 'SELL', amount, price)
    elif type_ == 'stop':
        order = ib.stopOrder('BUY' if side == 'buy' else 'SELL', amount, price)
    else:
        return jsonify({'error': 'Invalid order type'}), 400
    trade = ib.placeOrder(contract, order)
    while not trade.isDone():
        ib.sleep(1)
    ib.disconnect()
    log_order({'exchange': 'ib', 'order': trade.order.__dict__})
    return jsonify({'status': trade.orderStatus.status, 'order': trade.order.__dict__})

@app.route('/risk_management', methods=['POST'])
def risk_management():
    data = request.json
    balance = float(data['balance'])
    risk_per_trade = float(data.get('risk_per_trade', 0.01))
    win_rate = float(data.get('win_rate', 0.5))
    win_loss_ratio = float(data.get('win_loss_ratio', 2))
    fixed = fixed_fractional(balance, risk_per_trade)
    kelly = kelly_criterion(win_rate, win_loss_ratio)
    return jsonify({'fixed_fractional': fixed, 'kelly_criterion': kelly})

@app.route('/order_log', methods=['GET'])
def order_log():
    if os.path.exists(ORDER_LOG):
        with open(ORDER_LOG, 'r') as f:
            orders = json.load(f)
    else:
        orders = []
    return jsonify({'orders': orders})

if __name__ == '__main__':
    app.run(port=5007, debug=True) 