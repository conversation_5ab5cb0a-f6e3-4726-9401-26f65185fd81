import 'dart:convert';
import 'package:http/http.dart' as http;

class SignBridge {
  static Future<Map<String, String>?> signTrade(Map<String, dynamic> trade) async {
    final url = Uri.parse('http://localhost:5009/sign_trade');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(trade),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return {'tx_hash': data['tx_hash'], 'signature': data['signature']};
    } else {
      return null;
    }
  }

  static Future<bool> verifyTrade(String txHash, String signature) async {
    final url = Uri.parse('http://localhost:5009/verify_trade');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'tx_hash': txHash, 'signature': signature}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['valid'] == true;
    } else {
      return false;
    }
  }
} 