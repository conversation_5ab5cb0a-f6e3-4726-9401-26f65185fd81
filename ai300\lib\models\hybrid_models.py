import torch
import torch.nn as nn
import numpy as np
import tensorflow as tf
import tensorflow.lite as tflite

# نموذج LSTM
class LSTMModel(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=1):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
    def forward(self, x):
        out, _ = self.lstm(x)
        out = self.fc(out[:, -1, :])
        return out

# نموذج CNN
class CNNModel(nn.Module):
    def __init__(self, input_size=1, output_size=1):
        super().__init__()
        self.conv1 = nn.Conv1d(input_size, 32, 3, padding=1)
        self.relu = nn.ReLU()
        self.conv2 = nn.Conv1d(32, 16, 3, padding=1)
        self.fc = nn.Linear(16, output_size)
    def forward(self, x):
        x = x.permute(0,2,1)
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = x.mean(dim=2)
        x = self.fc(x)
        return x

# نموذج Transformer-XL (مبسط)
class SimpleTransformerXL(nn.Module):
    def __init__(self, input_size=1, d_model=64, nhead=4, num_layers=2, output_size=1):
        super().__init__()
        self.embedding = nn.Linear(input_size, d_model)
        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.fc = nn.Linear(d_model, output_size)
    def forward(self, x):
        x = self.embedding(x)
        x = self.transformer(x)
        x = self.fc(x.mean(dim=1))
        return x

# تدريب وحفظ النماذج
if __name__ == '__main__':
    # بيانات وهمية للتجربة
    X = np.random.randn(100, 30, 1).astype(np.float32)
    y = np.random.randn(100, 1).astype(np.float32)
    X_torch = torch.tensor(X)
    y_torch = torch.tensor(y)

    # LSTM
    lstm = LSTMModel()
    optimizer = torch.optim.Adam(lstm.parameters(), lr=0.001)
    for epoch in range(10):
        optimizer.zero_grad()
        out = lstm(X_torch)
        loss = ((out - y_torch)**2).mean()
        loss.backward()
        optimizer.step()
    torch.jit.save(torch.jit.script(lstm), 'lstm_model.pt')

    # CNN
    cnn = CNNModel()
    optimizer = torch.optim.Adam(cnn.parameters(), lr=0.001)
    for epoch in range(10):
        optimizer.zero_grad()
        out = cnn(X_torch)
        loss = ((out - y_torch)**2).mean()
        loss.backward()
        optimizer.step()
    torch.jit.save(torch.jit.script(cnn), 'cnn_model.pt')

    # Transformer-XL
    txl = SimpleTransformerXL()
    optimizer = torch.optim.Adam(txl.parameters(), lr=0.001)
    for epoch in range(10):
        optimizer.zero_grad()
        out = txl(X_torch)
        loss = ((out - y_torch)**2).mean()
        loss.backward()
        optimizer.step()
    torch.jit.save(torch.jit.script(txl), 'transformerxl_model.pt')

    # تصدير LSTM إلى tflite (مثال)
    import tensorflow as tf
    from tensorflow import keras
    # نموذج بسيط مكافئ في Keras
    keras_lstm = keras.Sequential([
        keras.layers.LSTM(64, input_shape=(30,1)),
        keras.layers.Dense(1)
    ])
    keras_lstm.compile(optimizer='adam', loss='mse')
    keras_lstm.fit(X, y, epochs=2, verbose=0)
    converter = tf.lite.TFLiteConverter.from_keras_model(keras_lstm)
    tflite_model = converter.convert()
    with open('lstm_model.tflite', 'wb') as f:
        f.write(tflite_model) 