import 'package:flutter_test/flutter_test.dart';
import 'package:ai_300/models/data_provider.dart';
import 'package:ai_300/security/security_service.dart';
import 'dart:developer';

void main() {
  test('Performance: DataProvider.getAlphaVantageOHLCV', () async {
    final sw = Stopwatch()..start();
    final data = await DataProvider.getAlphaVantageOHLCV(symbol: 'AAPL');
    sw.stop();
    print('AlphaVantage fetch time: ${sw.elapsedMilliseconds} ms, rows: ${data.length}');
    expect(data, isA<List>());
  });

  test('Performance: SecurityService.encryptText', () {
    final sw = Stopwatch()..start();
    final encrypted = SecurityService.encryptText('test performance');
    final decrypted = SecurityService.decryptText(encrypted);
    sw.stop();
    print('Encrypt/Decrypt time: ${sw.elapsedMicroseconds} μs');
    expect(decrypted, 'test performance');
  });

  // اختبار استهلاك الذاكرة (تقريبي)
  test('Memory usage (mock)', () async {
    final memBefore = ProcessInfo.currentRss;
    await DataProvider.getAlphaVantageOHLCV(symbol: 'AAPL');
    final memAfter = ProcessInfo.currentRss;
    print('Memory before: $memBefore, after: $memAfter, diff: ${memAfter - memBefore}');
    expect(memAfter, greaterThan(memBefore));
  });
} 