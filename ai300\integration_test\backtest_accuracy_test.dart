import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ai_300/ai_bridge.dart';
import 'dart:math';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  test('Backtest accuracy >= 93%', () async {
    // بيانات تاريخية وهمية (استبدلها ببيانات حقيقية في الإنتاج)
    List<double> closes = List.generate(100, (i) => 10000 + 10 * i + (i % 7 - 3) * 5);
    List<double> targets = List.generate(100, (i) => closes[i] + (i % 2 == 0 ? 10 : -10));
    int correct = 0;
    for (int i = 10; i < closes.length; i++) {
      final input = closes.sublist(i - 10, i);
      final pred = await AIBridge.predictLSTM(input);
      if (pred == null) continue;
      final actual = targets[i];
      if ((pred - actual).abs() / actual < 0.07) correct++;
    }
    final accuracy = correct / (closes.length - 10);
    print('Backtest accuracy: ${(accuracy * 100).toStringAsFixed(2)}%');
    expect(accuracy >= 0.93, true, reason: 'Model accuracy should be >= 93%');
  });
} 