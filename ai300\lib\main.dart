import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'screens/dashboard_screen.dart';
import 'screens/charts_screen.dart';
import 'screens/analysis_screen.dart';
import 'screens/trading_screen.dart';
import 'screens/portfolio_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/help_screen.dart';
import 'screens/logout_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
// import 'package:sentry_flutter/sentry_flutter.dart';
// import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'screens/defi_screen.dart';
import 'screens/social_trading_screen.dart';
import 'screens/tutorial_game.dart';
import 'screens/user_guide_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'screens/backtest_screen.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
const storage = FlutterSecureStorage();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final prefs = await SharedPreferences.getInstance();
  String fontFamily = prefs.getString('fontFamily') ?? 'Cairo';
  double fontSize = prefs.getDouble('fontSize') ?? 16;
  Color primaryColor = Color(prefs.getInt('primaryColor') ?? Colors.deepPurple.value);
  Color accentColor = Color(prefs.getInt('accentColor') ?? Colors.amber.value);
  bool darkMode = prefs.getBool('darkMode') ?? false;
  const initializationSettingsAndroid = AndroidInitializationSettings('app_icon');
  const initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
  );
  await flutterLocalNotificationsPlugin.initialize(initializationSettings);

  // Datadog and Sentry initialization commented out for now
  /*
  final datadogSdk = DatadogSdk.instance;
  await datadogSdk.initialize(
    clientToken: 'YOUR_DATADOG_CLIENT_TOKEN',
    env: 'production',
    serviceName: 'ai_300_app',
    trackingConsent: TrackingConsent.granted,
    site: DatadogSite.eu1,
    rumConfiguration: DatadogRumConfiguration(
      applicationId: 'YOUR_DATADOG_APP_ID',
      sessionSampleRate: 100,
      telemetrySampleRate: 100,
      userActionTracking: true,
      longTaskTracking: true,
      viewTrackingStrategy: DatadogNavigationObserver(),
    ),
    loggingConfiguration: DatadogLoggingConfiguration(),
    traceConfiguration: DatadogTraceConfiguration(),
  );
  await SentryFlutter.init(
    (options) {
      options.dsn = 'YOUR_SENTRY_DSN';
      options.tracesSampleRate = 1.0;
    },
    appRunner: () => runApp(MyApp(
      fontFamily: fontFamily,
      fontSize: fontSize,
      primaryColor: primaryColor,
      accentColor: accentColor,
      darkMode: darkMode,
    )),
  );
  */

  runApp(MyApp(
    fontFamily: fontFamily,
    fontSize: fontSize,
    primaryColor: primaryColor,
    accentColor: accentColor,
    darkMode: darkMode,
  ));
}

class MyApp extends StatelessWidget {
  final String fontFamily;
  final double fontSize;
  final Color primaryColor;
  final Color accentColor;
  final bool darkMode;
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static void setLocale(BuildContext context, Locale newLocale) {
    _MyAppState? state = context.findAncestorStateOfType<_MyAppState>();
    state?.setLocale(newLocale);
  }
  const MyApp({super.key, required this.fontFamily, required this.fontSize, required this.primaryColor, required this.accentColor, required this.darkMode});
  @override
  Widget build(BuildContext context) => _MyApp(fontFamily: fontFamily, fontSize: fontSize, primaryColor: primaryColor, accentColor: accentColor, darkMode: darkMode);
}

class _MyApp extends StatefulWidget {
  final String fontFamily;
  final double fontSize;
  final Color primaryColor;
  final Color accentColor;
  final bool darkMode;
  const _MyApp({required this.fontFamily, required this.fontSize, required this.primaryColor, required this.accentColor, required this.darkMode});
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<_MyApp> {
  Locale? _locale;
  @override
  void initState() {
    super.initState();
    _loadLocale();
  }
  void setLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }
  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    String? lang = prefs.getString('language');
    if (lang != null) {
      setState(() {
        _locale = lang == 'ar' ? const Locale('ar', 'SA') : const Locale('en', 'US');
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    final textTheme = GoogleFonts.getFont(widget.fontFamily, textStyle: TextStyle(fontSize: widget.fontSize));
    return MaterialApp(
      navigatorKey: MyApp.navigatorKey,
      title: 'Signal Black Super Trading',
      theme: ThemeData(
        primaryColor: widget.primaryColor,
        colorScheme: ColorScheme.light(primary: widget.primaryColor, secondary: widget.accentColor),
        textTheme: TextTheme(bodyMedium: textTheme),
        fontFamily: widget.fontFamily,
      ),
      darkTheme: ThemeData.dark().copyWith(
        primaryColor: widget.primaryColor,
        colorScheme: ColorScheme.dark(primary: widget.primaryColor, secondary: widget.accentColor),
        textTheme: TextTheme(bodyMedium: textTheme),
        fontFamily: widget.fontFamily,
      ),
      themeMode: widget.darkMode ? ThemeMode.dark : ThemeMode.light,
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('ar', 'SA'),
      ],
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      locale: _locale,
      localeResolutionCallback: (locale, supportedLocales) {
        if (_locale != null) return _locale;
        for (var supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale?.languageCode) {
            return supportedLocale;
          }
        }
        return supportedLocales.first;
      },
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  MainScreenState createState() => MainScreenState();
}

class MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  final List<Map<String, dynamic>> _sidebarItems = [
    {'icon': Icons.home, 'title': 'لوحة التحكم', 'titleEn': 'Dashboard'},
    {'icon': Icons.show_chart, 'title': 'الشارتات', 'titleEn': 'Charts'},
    {'icon': Icons.analytics, 'title': 'التحليل', 'titleEn': 'Analysis'},
    {'icon': Icons.trending_up, 'title': 'التداول', 'titleEn': 'Trading'},
    {'icon': Icons.account_balance_wallet, 'title': 'المحفظة', 'titleEn': 'Portfolio'},
    {'icon': Icons.settings, 'title': 'الإعدادات', 'titleEn': 'Settings'},
    {'icon': Icons.help, 'title': 'المساعدة', 'titleEn': 'Help'},
    {'icon': Icons.logout, 'title': 'تسجيل الخروج', 'titleEn': 'Logout'},
  ];

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'سيجنال بلاك سوبر تريدنج' : 'Signal Black Super Trading'),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
              ),
              child: Text(
                isArabic ? 'القائمة' : 'Menu',
                style: const TextStyle(color: Colors.white, fontSize: 24),
              ),
            ),
            ..._sidebarItems.asMap().entries.map((entry) {
              int idx = entry.key;
              var item = entry.value;
              return ListTile(
                leading: Icon(item['icon']),
                title: Text(isArabic ? item['title'] : item['titleEn']),
                selected: _selectedIndex == idx,
                onTap: () {
                  setState(() {
                    _selectedIndex = idx;
                  });
                  Navigator.pop(context);
                },
              );
            }).toList(),
            ListTile(
              leading: Icon(Icons.science),
              title: Text('الاختبار الرجعي (Backtest)'),
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(builder: (_) => const BacktestScreen()));
              },
            ),
          ],
        ),
      ),
      body: _buildBody(_selectedIndex),
    );
  }

  Widget _buildBody(int index) {
    switch (index) {
      case 0:
        return const DashboardScreen();
      case 1:
        return const ChartsScreen();
      case 2:
        return const AnalysisScreen();
      case 3:
        return const TradingScreen();
      case 4:
        return const PortfolioScreen();
      case 5:
        return const SettingsScreen();
      case 6:
        return const HelpScreen();
      case 7:
        return const LogoutScreen();
      case 8:
        return const DeFiScreen();
      case 9:
        return const SocialTradingScreen();
      case 10:
        return const TutorialGameWidget();
      case 11:
        return UserGuideScreen();
      default:
        return const DashboardScreen();
    }
  }
}

Future<Map<String, dynamic>> fetchMarketData({String symbol = 'BTCUSDT'}) async {
  final response = await http.get(Uri.parse('https://api.binance.com/api/v3/ticker/price?symbol=$symbol'));
  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to load market data');
  }
}

Future<dynamic> fetchMarketDataHttp(String url) async {
  final response = await http.get(Uri.parse(url));
  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to load data');
  }
}

void initializeAIModels() {
  // TODO: Integrate TensorFlow Lite or PyTorch Mobile for predictions
}

Future<Database> initializeDatabase() async {
  final databasePath = await getDatabasesPath();
  final path = join(databasePath, 'trading.db');
  return await openDatabase(
    path,
    version: 1,
    onCreate: (db, version) async {
      await db.execute('''
        CREATE TABLE trades (
          id INTEGER PRIMARY KEY,
          symbol TEXT,
          price REAL,
          timestamp TEXT
        )
      ''');
    },
  );
}

void executeAutomatedTrade(String symbol, double price) {
  // TODO: Implement trading logic with ccxt
}