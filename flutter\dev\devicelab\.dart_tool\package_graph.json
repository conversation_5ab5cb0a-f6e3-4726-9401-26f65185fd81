{"roots": ["flutter_devicelab"], "packages": [{"name": "flutter_devicelab", "version": "0.0.0", "dependencies": ["_discoveryapis_commons", "archive", "args", "async", "checked_yaml", "collection", "convert", "crypto", "file", "gcloud", "google_identity_services_web", "googlea<PERSON>", "googleapis_auth", "http", "http_parser", "json_annotation", "logging", "meta", "metrics_center", "mime", "path", "petitparser", "platform", "process", "pub_semver", "pubspec_parse", "retry", "shelf", "shelf_static", "source_span", "stack_trace", "standard_message_codec", "stream_channel", "string_scanner", "term_glyph", "typed_data", "vm_service", "web", "webkit_inspection_protocol", "xml", "yaml"], "devDependencies": ["_fe_analyzer_shared", "analyzer", "boolean_selector", "coverage", "frontend_server_client", "glob", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "pool", "shelf_packages_handler", "shelf_web_socket", "source_map_stack_trace", "source_maps", "test", "test_api", "test_core", "watcher", "web_socket", "web_socket_channel"]}, {"name": "web_socket_channel", "version": "3.0.2", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web_socket", "version": "0.1.6", "dependencies": ["web"]}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "shelf_web_socket", "version": "2.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "coverage", "version": "1.11.1", "dependencies": ["args", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "analyzer", "version": "7.3.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "80.0.0", "dependencies": ["meta"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "retry", "version": "3.1.2", "dependencies": []}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "googleapis_auth", "version": "1.6.0", "dependencies": ["args", "crypto", "google_identity_services_web", "http", "http_parser"]}, {"name": "googlea<PERSON>", "version": "12.0.0", "dependencies": ["_discoveryapis_commons", "http"]}, {"name": "google_identity_services_web", "version": "0.3.3", "dependencies": ["meta", "web"]}, {"name": "gcloud", "version": "0.8.18", "dependencies": ["_discoveryapis_commons", "googlea<PERSON>", "http", "meta", "retry"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "_discoveryapis_commons", "version": "1.0.7", "dependencies": ["http", "http_parser", "meta"]}, {"name": "standard_message_codec", "version": "0.0.1+4", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "process", "version": "5.0.3", "dependencies": ["file", "path", "platform"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "metrics_center", "version": "1.0.13", "dependencies": ["_discoveryapis_commons", "crypto", "gcloud", "googlea<PERSON>", "googleapis_auth", "http"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "http", "version": "1.3.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}], "configVersion": 1}