import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:shared_preferences/shared_preferences.dart';

class VoiceCommandWidget extends StatefulWidget {
  final void Function(String command)? onCommand;
  const VoiceCommandWidget({super.key, this.onCommand});
  @override
  State<VoiceCommandWidget> createState() => _VoiceCommandWidgetState();
}

class _VoiceCommandWidgetState extends State<VoiceCommandWidget> {
  late stt.SpeechToText _speech;
  bool _listening = false;
  String _lastWords = '';

  @override
  void initState() {
    super.initState();
    _speech = stt.SpeechToText();
  }

  void _listen() async {
    if (!_listening) {
      bool available = await _speech.initialize();
      if (available) {
        setState(() => _listening = true);
        _speech.listen(onResult: (val) {
          setState(() => _lastWords = val.recognizedWords);
          if (val.finalResult) {
            _handleCommand(_lastWords);
            setState(() => _listening = false);
          }
        });
      }
    } else {
      _speech.stop();
      setState(() => _listening = false);
    }
  }

  void _handleCommand(String command) async {
    command = command.toLowerCase();
    if (command.contains('شارت') || command.contains('chart')) {
      widget.onCommand?.call('charts');
    } else if (command.contains('لوحة التحكم') || command.contains('dashboard')) {
      widget.onCommand?.call('dashboard');
    } else if (command.contains('تحليل') || command.contains('analysis')) {
      widget.onCommand?.call('analysis');
    } else if (command.contains('تداول') || command.contains('trading')) {
      widget.onCommand?.call('trading');
    } else if (command.contains('محفظة') || command.contains('portfolio')) {
      widget.onCommand?.call('portfolio');
    } else if (command.contains('إعدادات') || command.contains('settings')) {
      widget.onCommand?.call('settings');
    } else if (command.contains('مساعدة') || command.contains('help')) {
      widget.onCommand?.call('help');
    } else if (command.contains('خروج') || command.contains('logout')) {
      widget.onCommand?.call('logout');
    } else if (command.contains('تصدير') || command.contains('export')) {
      widget.onCommand?.call('export');
    } else if (command.contains('وضع ليلي') || command.contains('dark')) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('darkMode', true);
      widget.onCommand?.call('dark');
    } else if (command.contains('وضع نهاري') || command.contains('light')) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('darkMode', false);
      widget.onCommand?.call('light');
    } else if (command.contains('تحديث')) {
      widget.onCommand?.call('refresh');
    }
    // أضف أوامر أخرى حسب الحاجة
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        IconButton(
          icon: Icon(_listening ? Icons.mic : Icons.mic_none, color: _listening ? Colors.red : Colors.black),
          onPressed: _listen,
          tooltip: 'استمع للأوامر الصوتية',
        ),
        if (_lastWords.isNotEmpty)
          Text('آخر أمر: $_lastWords', style: const TextStyle(fontSize: 13)),
      ],
    );
  }
} 