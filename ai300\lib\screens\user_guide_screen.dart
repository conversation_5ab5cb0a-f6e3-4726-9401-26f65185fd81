import 'package:flutter/material.dart';

class UserGuideScreen extends StatelessWidget {
  final List<Map<String, String>> _pages = [
    {
      'title_ar': 'لوحة التحكم',
      'desc_ar': 'هنا تجد ملخص الحساب والمؤشرات الرئيسية.',
      'title_en': 'Dashboard',
      'desc_en': 'Here you find account summary and main indicators.',
      'img': 'assets/guide/dashboard.png',
    },
    {
      'title_ar': 'الشارتات',
      'desc_ar': 'شاهد حركة الأسعار والمؤشرات الفنية.',
      'title_en': 'Charts',
      'desc_en': 'View price action and technical indicators.',
      'img': 'assets/guide/charts.png',
    },
    {
      'title_ar': 'التداول',
      'desc_ar': 'نفذ صفقاتك بسهولة وأمان.',
      'title_en': 'Trading',
      'desc_en': 'Execute your trades easily and securely.',
      'img': 'assets/guide/trading.png',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    return Scaffold(
      appBar: AppBar(title: Text(isArabic ? 'دليل المستخدم' : 'User Guide')),
      body: PageView.builder(
        itemCount: _pages.length,
        itemBuilder: (context, index) {
          final page = _pages[index];
          return Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(page['img']!, height: 200, errorBuilder: (_, __, ___) => const Icon(Icons.image, size: 120)),
                const SizedBox(height: 24),
                Text(
                  isArabic ? page['title_ar']! : page['title_en']!,
                  style: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Text(
                  isArabic ? page['desc_ar']! : page['desc_en']!,
                  style: const TextStyle(fontSize: 20),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
} 