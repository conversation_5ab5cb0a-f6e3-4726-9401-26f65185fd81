import 'dart:convert';
import 'package:http/http.dart' as http;

class DDOSBridge {
  static Future<bool> trainIsolation(List<List<double>> X) async {
    final url = Uri.parse('http://localhost:5010/train_isolation');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'X': X}),
    );
    return response.statusCode == 200;
  }

  static Future<List<int>?> detectAnomaly(List<List<double>> X) async {
    final url = Uri.parse('http://localhost:5010/detect_anomaly');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'X': X}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['predictions'] as List).map((e) => (e as int)).toList();
    } else {
      return null;
    }
  }
} 