from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
from sklearn.ensemble import IsolationForest
import joblib
import os

app = Flask(__name__)
CORS(app)

MODEL_PATH = 'isoforest_ddos.pkl'

@app.route('/train_isolation', methods=['POST'])
def train_isolation():
    data = request.json
    X = np.array(data['X'])
    model = IsolationForest(contamination=0.01)
    model.fit(X)
    joblib.dump(model, MODEL_PATH)
    return jsonify({'status': 'trained'})

@app.route('/detect_anomaly', methods=['POST'])
def detect_anomaly():
    data = request.json
    X = np.array(data['X'])
    if not os.path.exists(MODEL_PATH):
        return jsonify({'error': 'Model not trained'}), 400
    model = joblib.load(MODEL_PATH)
    preds = model.predict(X).tolist()
    # -1 = anomaly, 1 = normal
    return jsonify({'predictions': preds})

if __name__ == '__main__':
    app.run(port=5010, debug=True) 