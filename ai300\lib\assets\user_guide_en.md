# User Guide - Signal Black Super Trading

## Introduction

Signal Black Super Trading is a smart, integrated trading platform supporting technical analysis, AI, automated trading, AR, voice control, localization, risk management, security, and interactive user support.

---

## Main Features

### 1. Main Screens
- **Dashboard**: Overview of account, assets, and alerts.
  - ![Dashboard Screenshot](assets/screens/dashboard.png)
  - [Short Video](assets/videos/dashboard.mp4)
- **Charts & Technical Analysis**: Candlesticks, indicators, and AI recommendations.
  - ![Charts Screenshot](assets/screens/charts.png)
  - [Short Video](assets/videos/charts.mp4)
- **Fundamental & Economic Analysis**: Yahoo Finance, Alpha Vantage, Economic Calendar data.
  - ![Analysis Screenshot](assets/screens/analysis.png)
- **Trading**: Market/Limit/Stop orders with risk management.
  - ![Trading Screenshot](assets/screens/trading.png)
- **Portfolio**: Performance, risk reports, Monte Carlo simulation.
  - ![Portfolio Screenshot](assets/screens/portfolio.png)
- **Settings**: Color, language, and security customization.
- **Help**: Interactive guide, FAQ.

### 2. AI & Recommendations
- **Hybrid Models (LSTM, CNN, Transformer)**: Accurate recommendations.
- **XAI/SHAP**: Explains the reason for each recommendation.
- **AutoML/River**: Continuous learning and self-improving models.

### 3. Automated Trading & Risk Management
- **Binance, MT5, IB Integration**: Automated order execution.
- **Risk Management**: Sharpe, Sortino, Calmar, Kelly, Fixed Fractional.
- **Monte Carlo Simulation**: Profit/loss probability forecasting.

### 4. Security & Reliability
- **MFA/OTP**: Two-factor authentication.
- **Digital Trade Signing**: Hyperledger Fabric.
- **DDoS/IsolationForest**: Anomaly detection.
- **Logging & Monitoring**: Sentry, Datadog.

### 5. Integrations & Testing
- **Performance & Coverage Tests**: Flutter DevTools, CI/CD, GitHub Actions.
- **Backtesting**: Model accuracy > 93%.

---

## Screenshots & Videos
- All screenshots and videos are included in `assets/screens` and `assets/videos` folders.

---

## Guide Updates
- This guide is updated automatically with every new feature.
- For support or questions: see the Help section in the app.

## Fundamental & Economic Data Sources

The application relies on trusted global data sources to fetch the latest financial and economic information:

- **Yahoo Finance**: For prices, dividends, annual yield, and key financial ratios.
- **Alpha Vantage**: For financial ratios, market capitalization, P/E ratio, EPS, and more.
- **Economic Calendar API**: For major economic events such as interest rates, inflation, GDP, unemployment, and global economic indicators.

The source of each piece of information is displayed below the data in the Fundamental & Economic Analysis screen.

> **Note:** All data is fetched live from the internet and updated automatically when the analysis screen is opened or when the refresh button is pressed.

## Backtest Examples

- You can enter your strategy code in Python (Zipline) and upload price data (CSV) via the "Backtest" screen.
- After clicking "Run Backtest", results will be displayed in a table.
- Example simple code:

```
def initialize(context):
    pass

def handle_data(context, data):
    pass
```

- Example price data:
```
date,open,high,low,close,volume
2023-01-01,100,110,90,105,1000
2023-01-02,105,115,95,110,1200
...
```

## AI Model Management

### Model Path:
- All trained models (e.g., LSTM, CNN) should be placed in: `lib/assets/models/`
- Example files: `lstm_model.tflite`, `cnn_model.tflite`

### Adding or Updating a Model:
1. Place the model file in `lib/assets/models/`
2. Add its name to the `assets` section in `pubspec.yaml`:
   ```yaml
   assets:
     - assets/models/lstm_model.tflite
     - assets/models/cnn_model.tflite
   ```
3. Run `flutter pub get` after any update.

### Testing Models from the UI:
- Go to the Analysis screen.
- Click the "Test Model Loading & Prediction" button.
- The prediction result for each model (LSTM, CNN) will be displayed instantly.

### Technical Notes:
- You can add support for PyTorch Mobile models (`.pt`) or REST for advanced models later.
- To update a model: just replace the file and restart the app.
- All code supports dynamic loading of models from the above path.

--- 