import requests
import pandas as pd
import time
import logging

def fetch_binance_ohlc(symbol='BTCUSDT', interval='1h', limit=1000):
    url = f'https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}'
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        if not data or not isinstance(data, list):
            logging.warning(f'Binance API returned empty or invalid data for {symbol}')
            return None
        df = pd.DataFrame(data, columns=[
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base', 'taker_buy_quote', 'ignore'])
        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)
        df['date'] = pd.to_datetime(df['open_time'], unit='ms')
        return df[['date', 'open', 'high', 'low', 'close', 'volume']]
    except Exception as e:
        logging.error(f'Error fetching Binance data for {symbol}: {e}')
        return None

def fetch_yahoo(symbol='AAPL', interval='1d', range_='1y'):
    url = f'https://query1.finance.yahoo.com/v8/finance/chart/{symbol}?interval={interval}&range={range_}'
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        if 'chart' not in data or not data['chart']['result']:
            logging.warning(f'Yahoo API returned empty data for {symbol}')
            return None
        data = data['chart']['result'][0]
        timestamps = data['timestamp']
        indicators = data['indicators']['quote'][0]
        df = pd.DataFrame({
            'date': pd.to_datetime(timestamps, unit='s'),
            'open': indicators['open'],
            'high': indicators['high'],
            'low': indicators['low'],
            'close': indicators['close'],
            'volume': indicators['volume'],
        })
        return df
    except Exception as e:
        logging.error(f'Error fetching Yahoo data for {symbol}: {e}')
        return None

def fetch_coingecko(symbol_id='bitcoin', days=365):
    url = f'https://api.coingecko.com/api/v3/coins/{symbol_id}/market_chart?vs_currency=usd&days={days}'
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        if 'prices' not in data:
            logging.warning(f'CoinGecko API returned empty data for {symbol_id}')
            return None
        prices = data['prices']
        df = pd.DataFrame(prices, columns=['timestamp', 'price'])
        df['date'] = pd.to_datetime(df['timestamp'], unit='ms')
        return df[['date', 'price']]
    except Exception as e:
        logging.error(f'Error fetching CoinGecko data for {symbol_id}: {e}')
        return None

def main():
    logging.basicConfig(filename='data_collection.log', level=logging.INFO)
    # Binance
    for symbol in ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT']:
        df = fetch_binance_ohlc(symbol, '1h', 1000)
        if df is not None:
            df.to_csv(f'{symbol.lower()}_binance.csv', index=False)
            logging.info(f'Saved {symbol} Binance data.')
    # Yahoo
    for symbol in ['AAPL', 'MSFT', 'TSLA', 'GOOGL']:
        df = fetch_yahoo(symbol, '1d', '1y')
        if df is not None:
            df.to_csv(f'{symbol.lower()}_yahoo.csv', index=False)
            logging.info(f'Saved {symbol} Yahoo data.')
    # CoinGecko
    for symbol_id in ['bitcoin', 'ethereum', 'binancecoin', 'solana']:
        df = fetch_coingecko(symbol_id, 365)
        if df is not None:
            df.to_csv(f'{symbol_id}_coingecko.csv', index=False)
            logging.info(f'Saved {symbol_id} CoinGecko data.')
    print('تم جمع البيانات بنجاح. راجع ملف السجل data_collection.log لأي أخطاء.')

if __name__ == '__main__':
    main() 