import 'package:flutter_otp/flutter_otp.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:otp/otp.dart';
import 'dart:convert';
import 'dart:io';

class SecurityService {
  static final _otp = FlutterOtp();
  static final _aesKey = encrypt.Key.fromUtf8('my32lengthsupersecretnooneknows1'); // 32 chars
  static final _iv = encrypt.IV.fromLength(16);
  static final _encrypter = encrypt.Encrypter(encrypt.AES(_aesKey));

  // توليد OTP عبر SMS (للتوافق)
  static String generateOTP(String phone) {
    return _otp.generateOTP(length: 6);
  }

  // تحقق OTP
  static bool verifyOTP(String entered, String sent) {
    return entered == sent;
  }

  // توليد TOTP (Google Authenticator)
  static String generateTOTP(String secret) {
    return OTP.generateTOTPCodeString(secret, DateTime.now().millisecondsSinceEpoch);
  }

  // تحقق TOTP
  static bool verifyTOTP(String secret, String entered) {
    final code = generateTOTP(secret);
    return entered == code;
  }

  // إرسال OTP عبر البريد (واجهة فقط)
  static Future<void> sendOTPEmail(String email, String code) async {
    // هنا يمكن ربط SMTP أو API مثل SendGrid
    // مثال: await sendEmail(email, 'رمز التحقق', 'رمزك: $code');
  }

  // تشفير نص
  static String encryptText(String plain) {
    final encrypted = _encrypter.encrypt(plain, iv: _iv);
    return encrypted.base64;
  }

  // فك تشفير نص
  static String decryptText(String encrypted) {
    return _encrypter.decrypt64(encrypted, iv: _iv);
  }

  // تشفير ملف كامل
  static Future<void> encryptFile(String inputPath, String outputPath) async {
    final bytes = await File(inputPath).readAsBytes();
    final encrypted = _encrypter.encryptBytes(bytes, iv: _iv);
    await File(outputPath).writeAsBytes(encrypted.bytes);
  }

  // فك تشفير ملف كامل
  static Future<void> decryptFile(String inputPath, String outputPath) async {
    final bytes = await File(inputPath).readAsBytes();
    final decrypted = _encrypter.decryptBytes(encrypt.Encrypted(bytes), iv: _iv);
    await File(outputPath).writeAsBytes(decrypted);
  }
} 