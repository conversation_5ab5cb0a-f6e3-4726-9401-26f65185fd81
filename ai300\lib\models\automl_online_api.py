from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
import pandas as pd
from tpot import TPOTRegressor
import river
import joblib
import os
import onnxruntime as ort
from stable_baselines3 import PPO
import torch
import transformers

app = Flask(__name__)
CORS(app)

MODEL_PATH = 'automl_tpot_model.pkl'
ONLINE_MODEL_PATH = 'river_online_model.pkl'
RL_MODEL_PATH = 'rl_model.zip'
ONNX_MODEL_PATH = 'onnx_model.onnx'
DEEPSPEED_MODEL_PATH = 'deepspeed_model.pt'
HUGGINGFACE_MODEL = 'distilbert-base-uncased-finetuned-sst-2-english'

@app.route('/automl_train', methods=['POST'])
def automl_train():
    data = request.json
    X = np.array(data['X'])
    y = np.array(data['y'])
    tpot = TPOTRegressor(generations=5, population_size=20, verbosity=2)
    tpot.fit(X, y)
    joblib.dump(tpot.fitted_pipeline_, MODEL_PATH)
    return jsonify({'status': 'trained', 'score': tpot.score(X, y)})

@app.route('/automl_predict', methods=['POST'])
def automl_predict():
    data = request.json
    X = np.array(data['X'])
    if not os.path.exists(MODEL_PATH):
        return jsonify({'error': 'Model not trained'}), 400
    model = joblib.load(MODEL_PATH)
    preds = model.predict(X).tolist()
    return jsonify({'predictions': preds})

@app.route('/online_learn', methods=['POST'])
def online_learn():
    data = request.json
    x = data['x']
    y = data['y']
    if os.path.exists(ONLINE_MODEL_PATH):
        model = joblib.load(ONLINE_MODEL_PATH)
    else:
        model = river.linear_model.LinearRegression()
    model.learn_one(x, y)
    joblib.dump(model, ONLINE_MODEL_PATH)
    return jsonify({'status': 'updated'})

@app.route('/online_predict', methods=['POST'])
def online_predict():
    data = request.json
    x = data['x']
    if not os.path.exists(ONLINE_MODEL_PATH):
        return jsonify({'error': 'Online model not trained'}), 400
    model = joblib.load(ONLINE_MODEL_PATH)
    pred = model.predict_one(x)
    return jsonify({'prediction': pred})

@app.route('/predict', methods=['POST'])
def predict_any():
    data = request.json
    model = data.get('model')
    input_data = data.get('input')
    # AutoML
    if model == 'automl':
        if not os.path.exists(MODEL_PATH):
            return jsonify({'error': 'Model not trained'}), 400
        mdl = joblib.load(MODEL_PATH)
        preds = mdl.predict([input_data]).tolist()
        return str(preds[0])
    # RL
    elif model == 'rl':
        if not os.path.exists(RL_MODEL_PATH):
            return jsonify({'error': 'RL model not trained'}), 400
        mdl = PPO.load(RL_MODEL_PATH)
        obs = input_data
        action, _ = mdl.predict(obs)
        return str(float(action))
    # ONNX
    elif model == 'onnx':
        if not os.path.exists(ONNX_MODEL_PATH):
            return jsonify({'error': 'ONNX model not found'}), 400
        sess = ort.InferenceSession(ONNX_MODEL_PATH)
        input_name = sess.get_inputs()[0].name
        output = sess.run(None, {input_name: [input_data]})
        return str(float(output[0][0][0]))
    # River
    elif model == 'river':
        if not os.path.exists(ONLINE_MODEL_PATH):
            return jsonify({'error': 'Online model not trained'}), 400
        mdl = joblib.load(ONLINE_MODEL_PATH)
        pred = mdl.predict_one({str(i): v for i, v in enumerate(input_data)})
        return str(float(pred))
    # DeepSpeed
    elif model == 'deepspeed':
        if not os.path.exists(DEEPSPEED_MODEL_PATH):
            return jsonify({'error': 'DeepSpeed model not found'}), 400
        mdl = torch.jit.load(DEEPSPEED_MODEL_PATH)
        inp = torch.tensor(input_data, dtype=torch.float32)
        out = mdl(inp)
        return str(float(out.detach().cpu().numpy().flatten()[0]))
    # HuggingFace
    elif model == 'huggingface':
        tokenizer = transformers.AutoTokenizer.from_pretrained(HUGGINGFACE_MODEL)
        model_hf = transformers.AutoModelForSequenceClassification.from_pretrained(HUGGINGFACE_MODEL)
        inputs = tokenizer(str(input_data), return_tensors='pt')
        outputs = model_hf(**inputs)
        pred = torch.softmax(outputs.logits, dim=1).detach().cpu().numpy().flatten().tolist()
        return str(float(pred[1]))
    # TransformerXL
    elif model == 'transformerxl':
        pt_path = 'assets/models/transformerxl_model.pt'
        if not os.path.exists(pt_path):
            return jsonify({'error': 'TransformerXL model not found'}), 400
        mdl = torch.jit.load(pt_path)
        inp = torch.tensor(input_data, dtype=torch.float32)
        out = mdl(inp)
        return str(float(out.detach().cpu().numpy().flatten()[0]))
    return jsonify({'error': 'Unknown model'}), 400

if __name__ == '__main__':
    app.run(port=5006, debug=True) 