import numpy as np

def fixed_fractional(equity, risk_per_trade):
    # نسبة مئوية ثابتة من رأس المال
    return equity * risk_per_trade

def kelly_criterion(win_rate, win_loss_ratio):
    # Kelly = W - (1-W)/R
    kelly = win_rate - (1 - win_rate) / win_loss_ratio
    return max(0, kelly)

def sharpe_ratio(returns, risk_free_rate=0.0):
    excess = np.array(returns) - risk_free_rate
    return np.mean(excess) / (np.std(excess) + 1e-8)

def sortino_ratio(returns, risk_free_rate=0.0):
    downside = np.std([r for r in returns if r < risk_free_rate])
    return (np.mean(returns) - risk_free_rate) / (downside + 1e-8)

def calmar_ratio(returns):
    max_drawdown = np.min(np.cumprod(1 + np.array(returns)) / np.maximum.accumulate(np.cumprod(1 + np.array(returns)))) - 1
    return np.mean(returns) / abs(max_drawdown + 1e-8)

def monte_carlo_simulation(start_equity, returns, n_sim=1000, horizon=100):
    simulations = []
    for _ in range(n_sim):
        equity = start_equity
        path = [equity]
        for _ in range(horizon):
            r = np.random.choice(returns)
            equity *= (1 + r)
            path.append(equity)
        simulations.append(path)
    return simulations

if __name__ == '__main__':
    # مثال استخدام سريع
    returns = np.random.normal(0.001, 0.02, 252)
    print('Sharpe:', sharpe_ratio(returns))
    print('Sortino:', sortino_ratio(returns))
    print('Calmar:', calmar_ratio(returns))
    sims = monte_carlo_simulation(10000, returns)
    print('Monte Carlo آخر قيمة:', [s[-1] for s in sims[:5]]) 