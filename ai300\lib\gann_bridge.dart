import 'dart:convert';
import 'package:http/http.dart' as http;

class GannBridge {
  static Future<Map<String, dynamic>?> analyzeGannCycles(List<double> closes) async {
    final url = Uri.parse('http://localhost:5004/gann_cycles');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'close': closes}),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }
} 