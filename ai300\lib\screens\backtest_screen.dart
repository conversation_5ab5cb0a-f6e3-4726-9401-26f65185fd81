import 'package:flutter/material.dart';
import '../quant_bridge.dart';

class BacktestScreen extends StatefulWidget {
  const BacktestScreen({Key? key}) : super(key: key);
  @override
  State<BacktestScreen> createState() => _BacktestScreenState();
}

class _BacktestScreenState extends State<BacktestScreen> {
  TextEditingController strategyController = TextEditingController(text: 'def initialize(context):\n    pass\ndef handle_data(context, data):\n    pass');
  TextEditingController dataController = TextEditingController();
  TextEditingController startController = TextEditingController(text: '2023-01-01');
  TextEditingController endController = TextEditingController(text: '2023-12-31');
  bool loading = false;
  Map<String, dynamic>? results;
  String? error;

  Future<void> runBacktest() async {
    setState(() { loading = true; error = null; results = null; });
    final res = await QuantBridge.runBacktest(
      strategyCode: strategyController.text,
      dataCsv: dataController.text,
      start: startController.text,
      end: endController.text,
    );
    if (res != null && res['results'] != null) {
      setState(() { results = res['results']; loading = false; });
    } else {
      setState(() { error = res?['error'] ?? 'Unknown error'; loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الاختبار الرجعي (Backtest)')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('كود الاستراتيجية (Python/Zipline):', style: TextStyle(fontWeight: FontWeight.bold)),
              TextField(
                controller: strategyController,
                minLines: 6,
                maxLines: 16,
                decoration: const InputDecoration(border: OutlineInputBorder()),
              ),
              const SizedBox(height: 12),
              const Text('بيانات الأسعار (CSV):', style: TextStyle(fontWeight: FontWeight.bold)),
              TextField(
                controller: dataController,
                minLines: 6,
                maxLines: 16,
                decoration: const InputDecoration(border: OutlineInputBorder(), hintText: 'date,open,high,low,close,volume'),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: startController,
                      decoration: const InputDecoration(labelText: 'تاريخ البداية (YYYY-MM-DD)'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: endController,
                      decoration: const InputDecoration(labelText: 'تاريخ النهاية (YYYY-MM-DD)'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.play_arrow),
                label: const Text('تشغيل الاختبار الرجعي'),
                onPressed: loading ? null : runBacktest,
              ),
              if (loading) const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              ),
              if (error != null) Text('خطأ: $error', style: const TextStyle(color: Colors.red)),
              if (results != null) ...[
                const Divider(),
                const Text('نتائج الاختبار:', style: TextStyle(fontWeight: FontWeight.bold)),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columns: [
                      for (final k in results!.keys) DataColumn(label: Text(k)),
                    ],
                    rows: [
                      for (int i = 0; i < (results![results!.keys.first] as List).length; i++)
                        DataRow(cells: [
                          for (final k in results!.keys) DataCell(Text('${results![k][i]}')),
                        ]),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 