import numpy as np
import pandas as pd
from scipy.signal import find_peaks, correlate
from scipy.fft import fft, fftfreq
from textblob import TextBlob
import re
import json

# 1. تحليل Gann (زوايا ومربعات)
def gann_angles(prices, step=45):
    # يحسب زوايا Gann (45, 90, 135, ...)
    angles = {}
    for angle in range(step, 360, step):
        slope = np.tan(np.radians(angle))
        base = prices[0]
        line = [base + slope * (i) for i in range(len(prices))]
        angles[angle] = line
    return angles

def gann_square_of_n(n):
    # مربعات Gann
    square = np.array([[i*j for j in range(1, n+1)] for i in range(1, n+1)])
    return square

def gann_plot_data(prices, step=45):
    # يُعيد خطوط الزوايا (x, y) لكل زاوية لرسمها في Flutter
    lines = []
    for angle, line in gann_angles(prices, step).items():
        lines.append({'angle': angle, 'x': list(range(len(prices))), 'y': list(line)})
    return lines

# 2. اكتشاف الدورات الزمنية (Fourier/Autocorrelation)
def detect_cycles(prices):
    # تحليل فورييه
    n = len(prices)
    yf = fft(prices - np.mean(prices))
    xf = fftfreq(n, 1)
    amplitudes = np.abs(yf)
    # أكبر الدورات
    top_cycles = sorted(zip(xf[1:n//2], amplitudes[1:n//2]), key=lambda x: -x[1])[:3]
    return [{'period': 1/abs(c[0]), 'amplitude': c[1]} for c in top_cycles if c[0] != 0]

def autocorrelation_cycles(prices):
    ac = correlate(prices - np.mean(prices), prices - np.mean(prices), mode='full')
    ac = ac[ac.size//2:]
    peaks, _ = find_peaks(ac, distance=5)
    return peaks.tolist()

# 3. التحليل السلوكي (NLP/BERT/VADER)
def nlp_sentiment(texts, method='textblob'):
    if method == 'textblob':
        sentiments = [TextBlob(t).sentiment.polarity for t in texts]
    elif method == 'vader':
        from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
        analyzer = SentimentIntensityAnalyzer()
        sentiments = [analyzer.polarity_scores(t)['compound'] for t in texts]
    elif method == 'arabert':
        # يتطلب تثبيت arabert/transformers
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        import torch
        tokenizer = AutoTokenizer.from_pretrained('aubmindlab/bert-base-arabertv2')
        model = AutoModelForSequenceClassification.from_pretrained('aubmindlab/bert-base-arabertv2')
        sentiments = []
        for t in texts:
            inputs = tokenizer(t, return_tensors='pt', truncation=True, padding=True)
            outputs = model(**inputs)
            score = float(torch.softmax(outputs.logits, dim=1)[0][1])
            sentiments.append(score)
    else:
        sentiments = [0 for _ in texts]
    return {'mean': float(np.mean(sentiments)), 'std': float(np.std(sentiments)), 'all': [float(s) for s in sentiments]}

def nlp_keywords(texts, top_n=10):
    # استخراج الكلمات الأكثر تكراراً
    words = re.findall(r'\w+', ' '.join(texts).lower())
    freq = pd.Series(words).value_counts()
    return freq.head(top_n).to_dict()

# 4. اكتشاف الأنماط التوافقية (Harmonic Patterns)
def detect_harmonic_patterns(prices):
    patterns = []
    for i in range(len(prices)-4):
        x, a, b, c, d = prices[i:i+5]
        ab = abs((b-a)/(a-x+1e-8))
        bc = abs((c-b)/(b-a+1e-8))
        cd = abs((d-c)/(c-b+1e-8))
        # Gartley
        if 0.61 < ab < 0.65 and 0.38 < bc < 0.89 and 1.27 < cd < 1.618:
            patterns.append({'type': 'Gartley', 'start': i, 'end': i+4})
        # Bat
        if 0.38 < ab < 0.5 and 0.382 < bc < 0.886 and 1.618 < cd < 2.618:
            patterns.append({'type': 'Bat', 'start': i, 'end': i+4})
        # Butterfly
        if 0.786 < ab < 0.89 and 0.382 < bc < 0.886 and 1.618 < cd < 2.618:
            patterns.append({'type': 'Butterfly', 'start': i, 'end': i+4})
        # Crab
        if 0.382 < ab < 0.618 and 0.382 < bc < 0.886 and 2.24 < cd < 3.618:
            patterns.append({'type': 'Crab', 'start': i, 'end': i+4})
        # Shark
        if 0.886 < ab < 1.13 and 1.13 < bc < 1.618 and 1.618 < cd < 2.24:
            patterns.append({'type': 'Shark', 'start': i, 'end': i+4})
    return patterns

# 5. تصدير النتائج لواجهة Flutter (JSON)
def export_analysis_to_json(prices, texts=None):
    result = {
        'gann_angles': gann_plot_data(prices),
        'cycles': detect_cycles(prices),
        'autocorr_peaks': autocorrelation_cycles(prices),
        'harmonic_patterns': detect_harmonic_patterns(prices),
    }
    if texts:
        result['sentiment'] = nlp_sentiment(texts)
        result['keywords'] = nlp_keywords(texts)
    return json.dumps(result, ensure_ascii=False)

if __name__ == '__main__':
    # مثال بيانات سعرية وهمية
    prices = np.cumsum(np.random.randn(100)) + 100
    texts = ['The market is bullish!', 'Crash is coming!', 'Sideways action expected.', 'السوق السعودي قوي اليوم']
    print('Gann Angles:', gann_angles(prices))
    print('Gann Square of 9:', gann_square_of_n(9))
    print('Gann Plot Data:', gann_plot_data(prices))
    print('Top Cycles (Fourier):', detect_cycles(prices))
    print('Autocorrelation Peaks:', autocorrelation_cycles(prices))
    print('Sentiment (TextBlob):', nlp_sentiment(texts, method='textblob'))
    # print('Sentiment (VADER):', nlp_sentiment(texts, method='vader'))
    # print('Sentiment (AraBERT):', nlp_sentiment(texts, method='arabert'))
    print('Keywords:', nlp_keywords(texts))
    print('Harmonic Patterns:', detect_harmonic_patterns(prices))
    print('Exported JSON:', export_analysis_to_json(prices, texts)) 