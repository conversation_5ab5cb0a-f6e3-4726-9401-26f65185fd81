{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "integration_test", "path": "D:\\\\ai 300\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "android": [{"name": "integration_test", "path": "D:\\\\ai 300\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "macos": [{"name": "integration_test_macos", "path": "D:\\\\ai 300\\\\flutter\\\\packages\\\\integration_test\\\\integration_test_macos\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "linux": [], "windows": [], "web": []}, "dependencyGraph": [{"name": "integration_test", "dependencies": []}, {"name": "integration_test_macos", "dependencies": []}], "date_created": "2025-05-24 15:00:21.776980", "version": "3.32.0", "swift_package_manager_enabled": {"ios": false, "macos": false}}