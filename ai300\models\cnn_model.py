import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import os

def build_cnn_model(input_shape):
    model = keras.Sequential([
        layers.Conv1D(32, 3, activation='relu', input_shape=input_shape),
        layers.Conv1D(16, 3, activation='relu'),
        layers.Flatten(),
        layers.Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    return model

def train_and_export(X_train, y_train, epochs=10):
    model = build_cnn_model((X_train.shape[1], X_train.shape[2]))
    model.fit(X_train, y_train, epochs=epochs, batch_size=32)
    # تصدير إلى TFLite
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    tflite_model = converter.convert()
    ASSETS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib', 'assets', 'models')
    os.makedirs(ASSETS_DIR, exist_ok=True)
    with open(os.path.join(ASSETS_DIR, 'cnn_model.tflite'), 'wb') as f:
        f.write(tflite_model)
    print(f'CNN model exported to {os.path.join(ASSETS_DIR, "cnn_model.tflite")}')

# مثال على بيانات وهمية للتجربة
if __name__ == '__main__':
    X_train = np.random.randn(100, 30, 1).astype(np.float32)
    y_train = np.random.randn(100, 1).astype(np.float32)
    train_and_export(X_train, y_train) 