import 'package:flutter/material.dart';

class SocialTradingScreen extends StatefulWidget {
  const SocialTradingScreen({Key? key}) : super(key: key);

  @override
  State<SocialTradingScreen> createState() => _SocialTradingScreenState();
}

class _SocialTradingScreenState extends State<SocialTradingScreen> {
  final List<Map<String, dynamic>> _strategies = [
    {'name': 'استراتيجية 1', 'performance': 85, 'votes': 10},
    {'name': 'استراتيجية 2', 'performance': 72, 'votes': 5},
  ];
  final TextEditingController _strategyController = TextEditingController();

  void _addStrategy() {
    if (_strategyController.text.trim().isEmpty) return;
    setState(() {
      _strategies.add({
        'name': _strategyController.text.trim(),
        'performance': 0,
        'votes': 0,
      });
      _strategyController.clear();
    });
  }

  void _vote(int index) {
    setState(() {
      _strategies[index]['votes'] += 1;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('التداول الاجتماعي')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _strategyController,
              decoration: const InputDecoration(
                labelText: 'أضف استراتيجية جديدة',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _addStrategy,
              child: const Text('إضافة'),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: ListView.builder(
                itemCount: _strategies.length,
                itemBuilder: (context, index) {
                  final s = _strategies[index];
                  return Card(
                    child: ListTile(
                      title: Text(s['name']),
                      subtitle: Text('الأداء: ${s['performance']}% | التصويتات: ${s['votes']}'),
                      trailing: IconButton(
                        icon: const Icon(Icons.thumb_up),
                        onPressed: () => _vote(index),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
} 