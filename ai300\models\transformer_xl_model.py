import torch
import torch.nn as nn
import os

class SimpleTransformerXL(nn.Module):
    def __init__(self, input_dim, d_model=64, nhead=4, num_layers=2):
        super().__init__()
        self.embedding = nn.Linear(input_dim, d_model)
        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.fc = nn.Linear(d_model, 1)
    def forward(self, x):
        # x: (batch, seq, features)
        x = self.embedding(x)
        x = self.transformer(x)
        x = x.mean(dim=1)
        return self.fc(x)

def train_and_export(X_train, y_train, epochs=10):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleTransformerXL(X_train.shape[2]).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    loss_fn = nn.MSELoss()
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train = torch.tensor(y_train, dtype=torch.float32).to(device)
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        output = model(X_train)
        loss = loss_fn(output.squeeze(), y_train)
        loss.backward()
        optimizer.step()
    # تصدير TorchScript
    traced = torch.jit.trace(model, X_train)
    ASSETS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib', 'assets', 'models')
    os.makedirs(ASSETS_DIR, exist_ok=True)
    torch.jit.save(torch.jit.script(traced), os.path.join(ASSETS_DIR, 'transformerxl_model.pt'))
    print(f'Transformer-XL model exported to {os.path.join(ASSETS_DIR, "transformerxl_model.pt")}')

if __name__ == '__main__':
    import numpy as np
    X_train = np.random.randn(100, 30, 1).astype(np.float32)
    y_train = np.random.randn(100, 1).astype(np.float32)
    train_and_export(X_train, y_train) 