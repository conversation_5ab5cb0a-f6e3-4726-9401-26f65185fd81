import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../main.dart';

class LogoutScreen extends StatelessWidget {
  LogoutScreen({super.key});
  final storage = const FlutterSecureStorage();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: () async {
          await storage.deleteAll();
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const SignalBlackSuperTradingApp()),
          );
        },
        child: const Text('Logout'),
      ),
    );
  }
} 