import 'package:flutter_test/flutter_test.dart';
import 'package:ai_300/models/data_provider.dart';

void main() {
  group('DataProvider', () {
    test('Alpha Vantage OHLCV', () async {
      final data = await DataProvider.getAlphaVantageOHLCV(symbol: 'AAPL');
      expect(data, isA<List>());
      if (data.isNotEmpty) {
        expect(data.first, contains('open'));
      }
    });
    test('CoinGecko OHLCV', () async {
      final data = await DataProvider.getCoinGeckoOHLCV(coinId: 'bitcoin');
      expect(data, isA<List>());
    });
    test('OANDA OHLCV', () async {
      final data = await DataProvider.getOandaOHLCV(instrument: 'EUR_USD');
      expect(data, isA<List>());
    });
    test('Finnhub OHLCV', () async {
      final data = await DataProvider.getFinnhubOHLCV(symbol: 'AAPL');
      expect(data, isA<List>());
    });
    test('StockTwits News', () async {
      final data = await DataProvider.getStockTwitsNews(symbol: 'AAPL');
      expect(data, isA<List>());
    });
    test('Economic Events', () async {
      final data = await DataProvider.getEconomicEvents(country: 'us');
      expect(data, isA<List>());
    });
  });
} 