import 'package:flutter/material.dart';
import 'package:web3dart/web3dart.dart';
import 'package:http/http.dart';

class DeFiScreen extends StatefulWidget {
  const DeFiScreen({Key? key}) : super(key: key);

  @override
  State<DeFiScreen> createState() => _DeFiScreenState();
}

class _DeFiScreenState extends State<DeFiScreen> {
  final TextEditingController _addressController = TextEditingController();
  String? _balance;
  bool _loading = false;

  Future<void> _getBalance() async {
    setState(() => _loading = true);
    try {
      final client = Web3Client('https://mainnet.infura.io/v3/YOUR_INFURA_KEY', Client());
      final address = EthereumAddress.fromHex(_addressController.text.trim());
      final balance = await client.getBalance(address);
      setState(() {
        _balance = balance.getValueInUnit(EtherUnit.ether).toString();
      });
      await client.dispose();
    } catch (e) {
      setState(() => _balance = 'خطأ: $e');
    }
    setState(() => _loading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('DeFi (Ethereum Balance)')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Ethereum Address',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loading ? null : _getBalance,
              child: _loading ? const CircularProgressIndicator() : const Text('جلب الرصيد'),
            ),
            const SizedBox(height: 24),
            if (_balance != null)
              Text('الرصيد: $_balance ETH', style: const TextStyle(fontSize: 20)),
          ],
        ),
      ),
    );
  }
} 