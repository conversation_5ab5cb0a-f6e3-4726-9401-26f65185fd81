import 'package:flutter/material.dart';
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';
import 'package:vector_math/vector_math_64.dart' as vector;

class ARScreen extends StatefulWidget {
  const ARScreen({Key? key}) : super(key: key);

  @override
  State<ARScreen> createState() => _ARScreenState();
}

class _ARScreenState extends State<ARScreen> {
  late ArCoreController arCoreController;

  @override
  void dispose() {
    arCoreController.dispose();
    super.dispose();
  }

  void _onArCoreViewCreated(ArCoreController controller) {
    arCoreController = controller;
    _addCandlestickChart();
  }

  void _addCandlestickChart() {
    // مثال: رسم 5 شموع يابانية ثلاثية الأبعاد
    final List<double> prices = [100, 110, 105, 120, 115];
    for (int i = 0; i < prices.length; i++) {
      final color = prices[i] >= 105 ? Colors.green : Colors.red;
      final node = ArCoreNode(
        shape: ArCoreCylinder(
          radius: 0.02,
          height: (prices[i] - 95) / 10,
          materials: [ArCoreMaterial(color: color)],
        ),
        position: vector.Vector3(i * 0.1 - 0.2, 0, -1),
      );
      arCoreController.addArCoreNode(node);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الواقع المعزز (AR)')),
      body: ArCoreView(
        onArCoreViewCreated: _onArCoreViewCreated,
        enableTapRecognizer: false,
      ),
    );
  }
} 