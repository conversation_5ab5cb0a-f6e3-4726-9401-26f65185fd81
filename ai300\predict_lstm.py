import sys
import numpy as np
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense

# قراءة بيانات الإغلاق من stdin (قائمة أرقام مفصولة بفواصل)
input_data = sys.stdin.read().strip()
closes = [float(x) for x in input_data.split(',') if x]

# إعداد البيانات (تحويلها إلى شكل مناسب لـ LSTM)
X = np.array(closes[-10:]).reshape((1, 10, 1))

# نموذج LSTM مبسط (يتم تدريبه سريعاً على نفس البيانات)
model = Sequential([
    LSTM(16, input_shape=(10, 1)),
    Dense(1)
])
model.compile(optimizer='adam', loss='mse')
# تدريب سريع (ليس عملياً لكنه تجريبي)
Y = np.array(closes[-10:]).reshape((1, 10, 1))
model.fit(X, Y[:, -1, :], epochs=10, verbose=0)

# توقع السعر التالي
pred = model.predict(X)[0][0]
print(pred) 