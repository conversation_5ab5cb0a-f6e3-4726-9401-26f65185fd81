name: signal_black_super_trading
version: 0.1.0
publish_to: none
description: Advanced Trading and Analysis Platform
environment:
  sdk: ">=2.17.0 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  fl_chart: ^0.63.0
  http: ^1.2.1
  provider: ^6.1.2
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic