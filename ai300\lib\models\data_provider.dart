import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

class DataProvider {
  static const _channel = MethodChannel('data_integration_channel');
  static const _binanceEventChannel = EventChannel('binance_ws_channel');

  /// جلب بيانات تاريخية/حية من بايثون لأي مصدر (موحد)
  static Future<Map<String, dynamic>> fetchData({
    required String source,
    required Map<String, dynamic> params,
  }) async {
    final result = await _channel.invokeMethod('fetchData', {
      'source': source,
      'params': params,
    });
    return Map<String, dynamic>.from(json.decode(result));
  }

  /// Alpha Vantage: OHLCV
  static Future<List<Map<String, dynamic>>> getAlphaVantageOHLCV({required String symbol, String interval = '1min'}) async {
    final res = await fetchData(source: 'alpha_vantage', params: {'symbol': symbol, 'interval': interval});
    return List<Map<String, dynamic>>.from(res['ohlcv'] ?? []);
  }

  /// CoinGecko: OHLCV
  static Future<List<Map<String, dynamic>>> getCoinGeckoOHLCV({required String coinId, String vsCurrency = 'usd'}) async {
    final res = await fetchData(source: 'coingecko', params: {'coin_id': coinId, 'vs_currency': vsCurrency});
    return List<Map<String, dynamic>>.from(res['ohlcv'] ?? []);
  }

  /// OANDA: OHLCV
  static Future<List<Map<String, dynamic>>> getOandaOHLCV({required String instrument, String granularity = 'M1'}) async {
    final res = await fetchData(source: 'oanda', params: {'instrument': instrument, 'granularity': granularity});
    return List<Map<String, dynamic>>.from(res['ohlcv'] ?? []);
  }

  /// Finnhub: OHLCV
  static Future<List<Map<String, dynamic>>> getFinnhubOHLCV({required String symbol}) async {
    final res = await fetchData(source: 'finnhub', params: {'symbol': symbol});
    return List<Map<String, dynamic>>.from(res['ohlcv'] ?? []);
  }

  /// StockTwits: News
  static Future<List<Map<String, dynamic>>> getStockTwitsNews({required String symbol}) async {
    final res = await fetchData(source: 'stocktwits', params: {'symbol': symbol});
    return List<Map<String, dynamic>>.from(res['news'] ?? []);
  }

  /// Economic Calendar: Events
  static Future<List<Map<String, dynamic>>> getEconomicEvents({String country = 'all'}) async {
    final res = await fetchData(source: 'economic', params: {'country': country});
    return List<Map<String, dynamic>>.from(res['events'] ?? []);
  }

  /// Binance WebSocket: بث حي (EventChannel)
  static Stream<dynamic> binanceWebSocket({required String symbol}) {
    // EventChannel من بايثون أو من HTTP SSE
    return _binanceEventChannel.receiveBroadcastStream({'symbol': symbol});
  }

  static Future<Map<String, dynamic>> getYahooFinanceSummary(String symbol) async {
    final url = 'https://query1.finance.yahoo.com/v10/finance/quoteSummary/$symbol?modules=summaryDetail,price,defaultKeyStatistics,financialData';
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['quoteSummary']['result'][0];
    } else {
      throw Exception('Yahoo Finance error: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> getAlphaVantageOverview(String symbol, String apiKey) async {
    final url = 'https://www.alphavantage.co/query?function=OVERVIEW&symbol=$symbol&apikey=$apiKey';
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Alpha Vantage error: ${response.body}');
    }
  }

  static Future<List<dynamic>> getEconomicEvents({String country = 'us'}) async {
    final url = 'https://economic-calendar-api.vercel.app/api/events?country=$country';
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      return jsonDecode(response.body)['events'];
    } else {
      throw Exception('Economic Calendar error: ${response.body}');
    }
  }
} 