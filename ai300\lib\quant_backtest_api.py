from flask import Flask, request, jsonify
from flask_cors import CORS
import zipline
from zipline.api import order, record, symbol
import pandas as pd
import numpy as np
import io
import base64

app = Flask(__name__)
CORS(app)

def run_zipline_backtest(strategy_code, data_csv, start, end, capital_base=10000):
    # إعداد البيئة
    data = pd.read_csv(io.StringIO(data_csv), parse_dates=['date'], index_col='date')
    data = data.sort_index()
    def initialize(context):
        exec(strategy_code, globals(), locals())
    def handle_data(context, data_):
        exec(strategy_code, globals(), locals())
    perf = zipline.run_algorithm(
        start=pd.Timestamp(start, tz='utc'),
        end=pd.Timestamp(end, tz='utc'),
        initialize=initialize,
        capital_base=capital_base,
        handle_data=handle_data,
        data_frequency='daily',
        bundle=None,
        trading_calendar=None,
        data=data
    )
    return perf.to_dict(orient='list')

@app.route('/backtest', methods=['POST'])
def backtest():
    data = request.json
    strategy_code = data['strategy_code']
    data_csv = data['data_csv']
    start = data['start']
    end = data['end']
    capital_base = data.get('capital_base', 10000)
    try:
        results = run_zipline_backtest(strategy_code, data_csv, start, end, capital_base)
        return jsonify({'results': results})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(port=5003, debug=True) 