import 'package:flame/game.dart';
import 'package:flutter/material.dart';

class TutorialGame extends FlameGame with HasTappables {
  @override
  Future<void> onLoad() async {
    overlays.add('TutorialOverlay');
  }
}

class TutorialGameWidget extends StatelessWidget {
  const TutorialGameWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GameWidget<TutorialGame>(
      game: TutorialGame(),
      overlayBuilderMap: {
        'TutorialOverlay': (ctx, game) => TutorialOverlay(),
      },
    );
  }
}

class TutorialOverlay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        color: Colors.black.withOpacity(0.7),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: const [
            Text('مرحباً بك في الوضع التعليمي!', style: TextStyle(color: Colors.white, fontSize: 22)),
            SizedBox(height: 8),
            Text('اضغط على العناصر للتجربة.\nيمكنك التنقل بين الشاشات والتعرف على الميزات.', style: TextStyle(color: Colors.white)),
            SizedBox(height: 8),
            Text('Welcome to Tutorial Mode!', style: TextStyle(color: Colors.white, fontSize: 22)),
            SizedBox(height: 8),
            Text('Tap on elements to try.\nYou can navigate and discover features.', style: TextStyle(color: Colors.white)),
          ],
        ),
      ),
    );
  }
} 