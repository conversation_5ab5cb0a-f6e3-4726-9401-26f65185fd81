import zipline
from zipline.api import order_target, record, symbol
from zipline.data import bundles
from zipline.utils.run_algo import run_algorithm
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import pytz
import numpy as np

# استراتيجية تقاطع المتوسطات المتحركة
class MovingAverageCrossStrategy:
    def __init__(self, short_window=10, long_window=30, asset='AAPL'):
        self.short_window = short_window
        self.long_window = long_window
        self.asset = asset

    def initialize(self, context):
        context.asset = symbol(self.asset)
        context.short_window = self.short_window
        context.long_window = self.long_window
        context.has_position = False

    def handle_data(self, context, data):
        short_mavg = data.history(context.asset, 'price', context.short_window, '1d').mean()
        long_mavg = data.history(context.asset, 'price', context.long_window, '1d').mean()
        if short_mavg > long_mavg and not context.has_position:
            order_target(context.asset, 100)
            context.has_position = True
        elif short_mavg < long_mavg and context.has_position:
            order_target(context.asset, 0)
            context.has_position = False
        record(price=data.current(context.asset, 'price'), short_mavg=short_mavg, long_mavg=long_mavg)


def run_backtest(strategy, start, end, capital_base=10000):
    perf = run_algorithm(
        start=pd.Timestamp(start, tz='utc'),
        end=pd.Timestamp(end, tz='utc'),
        initialize=strategy.initialize,
        handle_data=strategy.handle_data,
        capital_base=capital_base,
        data_frequency='daily',
        bundle='quantopian-quandl',
    )
    return perf

if __name__ == '__main__':
    # إعداد الاستراتيجية
    strategy = MovingAverageCrossStrategy(short_window=10, long_window=30, asset='AAPL')
    # تشغيل الباكتيست
    perf = run_backtest(strategy, '2017-01-01', '2018-01-01')
    # حساب العائد وSharpe
    returns = perf['portfolio_value'].pct_change().dropna()
    sharpe = returns.mean() / (returns.std() + 1e-8) * np.sqrt(252)
    print(f'Total Return: {perf["portfolio_value"].iloc[-1] / perf["portfolio_value"].iloc[0] - 1:.2%}')
    print(f'Sharpe Ratio: {sharpe:.2f}')
    # رسم الأداء
    plt.figure(figsize=(10,5))
    plt.plot(perf.index, perf['portfolio_value'], label='Portfolio Value')
    plt.title('Backtest Performance')
    plt.xlabel('Date')
    plt.ylabel('Portfolio Value')
    plt.legend()
    plt.tight_layout()
    plt.show() 