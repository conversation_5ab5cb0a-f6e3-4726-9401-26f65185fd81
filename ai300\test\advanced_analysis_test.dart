import 'package:flutter_test/flutter_test.dart';
import 'dart:convert';
import 'dart:math';

void main() {
  group('AdvancedAnalysis', () {
    test('Gann Angles', () {
      final prices = List.generate(20, (i) => 100 + i * 2);
      // توقع خطوط زوايا
      expect(prices.length, 20);
    });
    test('Detect Cycles', () {
      // دورة بسيطة
      final prices = List.generate(100, (i) => sin(i * 2 * pi / 20));
      expect(prices.length, 100);
    });
    test('Harmonic Patterns', () {
      // بيانات عشوائية
      final prices = List.generate(20, (i) => 100 + Random().nextDouble() * 10);
      expect(prices.length, 20);
    });
    test('NLP Sentiment', () {
      final texts = ['Good', 'Bad', 'Neutral'];
      expect(texts.length, 3);
    });
    test('Export JSON', () {
      final result = jsonEncode({'test': 1});
      expect(result, contains('test'));
    });
  });
} 