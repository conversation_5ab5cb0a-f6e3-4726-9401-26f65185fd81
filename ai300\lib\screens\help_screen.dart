import 'package:flutter/material.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  final List<Map<String, String>> indicatorsDoc = [
    {'name': 'Stochastic Oscillator', 'desc': 'مؤشر زخم يقيس موقع الإغلاق الحالي بالنسبة لنطاق الأسعار خلال فترة زمنية.'},
    {'name': 'ATR (Average True Range)', 'desc': 'مقياس لتقلب الأسعار، كلما زاد ATR زاد التذبذب.'},
    {'name': 'OBV (On-Balance Volume)', 'desc': 'مؤشر حجم يربط بين السعر وحجم التداول لتأكيد الاتجاه.'},
    {'name': 'Williams %R', 'desc': 'مؤشر زخم يقيس مستويات التشبع الشرائي والبيعي.'},
    {'name': 'VPT (Volume Price Trend)', 'desc': 'مؤشر يجمع بين تغير السعر وحجم التداول لقياس قوة الاتجاه.'},
    {'name': 'Momentum', 'desc': 'مؤشر يقيس سرعة تغير السعر.'},
    {'name': 'Demark Indicator', 'desc': 'مؤشر عدّ تسلسلي لتحديد نقاط الانعكاس.'},
    {'name': 'Accumulation/Distribution', 'desc': 'مؤشر يقيس تدفق الأموال إلى وخارج الأصل.'},
    {'name': 'Ichimoku Kinko Hyo', 'desc': 'نظام متكامل يحدد الدعم والمقاومة والاتجاه والزخم.'},
    {'name': 'Fibonacci Retracement', 'desc': 'مستويات دعم ومقاومة محتملة بناءً على نسب فيبوناتشي.'},
    {'name': 'Parabolic SAR', 'desc': 'مؤشر يتبع الاتجاه ويحدد نقاط الدخول والخروج.'},
  ];

  final List<Map<String, String>> patternsDoc = [
    {'name': 'Head & Shoulders', 'desc': 'نموذج انعكاسي يدل على نهاية الاتجاه الصاعد.'},
    {'name': 'Inverse Head & Shoulders', 'desc': 'نموذج انعكاسي يدل على نهاية الاتجاه الهابط.'},
    {'name': 'Rising Wedge', 'desc': 'نموذج استمراري أو انعكاسي، غالباً هبوطي.'},
    {'name': 'Falling Wedge', 'desc': 'نموذج استمراري أو انعكاسي، غالباً صعودي.'},
    {'name': 'Symmetrical Triangle', 'desc': 'نموذج تذبذب ضيق قد ينتهي بانفجار سعري.'},
    {'name': 'Ascending Triangle', 'desc': 'نموذج استمراري صاعد.'},
    {'name': 'Descending Triangle', 'desc': 'نموذج استمراري هابط.'},
    {'name': 'Flag', 'desc': 'نموذج استمراري قصير الأجل بعد حركة قوية.'},
    {'name': 'Pennant', 'desc': 'نموذج استمراري يشبه العلم لكن بتذبذب متناقص.'},
    {'name': 'Cup & Handle', 'desc': 'نموذج صعودي يتكون من قاع دائري ثم تصحيح بسيط.'},
  ];

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text('دليل المؤشرات الفنية', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
        ...indicatorsDoc.map((ind) => ListTile(
          title: Text(ind['name']!),
          subtitle: Text(ind['desc']!),
        )),
        const Text('دليل الأنماط الفنية', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
        ...patternsDoc.map((pat) => ListTile(
          title: Text(pat['name']!),
          subtitle: Text(pat['desc']!),
        )),
      ],
    );
  }
} 