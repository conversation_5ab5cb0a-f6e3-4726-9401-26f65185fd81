import requests
import time
import json
import websocket
from flask import Flask, request, jsonify, Response
import threading
import sqlite3
from datetime import datetime, timedelta
import queue
import importlib
import torch
import numpy as np

# Alpha Vantage
ALPHA_VANTAGE_API_KEY = 'YOUR_ALPHA_VANTAGE_KEY'
def get_alpha_vantage(symbol, interval='1min', outputsize='compact'):
    url = f'https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol={symbol}&interval={interval}&outputsize={outputsize}&apikey={ALPHA_VANTAGE_API_KEY}'
    r = requests.get(url)
    return r.json()

# CoinGecko
def get_coingecko_price(coin_id='bitcoin', vs_currency='usd'):
    url = f'https://api.coingecko.com/api/v3/simple/price?ids={coin_id}&vs_currencies={vs_currency}'
    r = requests.get(url)
    return r.json()

def get_coingecko_ohlc(coin_id='bitcoin', vs_currency='usd', days=1):
    url = f'https://api.coingecko.com/api/v3/coins/{coin_id}/ohlc?vs_currency={vs_currency}&days={days}'
    r = requests.get(url)
    return r.json()

# OANDA (REST)
OANDA_API_KEY = 'YOUR_OANDA_KEY'
OANDA_ACCOUNT_ID = 'YOUR_OANDA_ACCOUNT_ID'
def get_oanda_candles(instrument='EUR_USD', granularity='M1', count=100):
    url = f'https://api-fxpractice.oanda.com/v3/instruments/{instrument}/candles?granularity={granularity}&count={count}'
    headers = {'Authorization': f'Bearer {OANDA_API_KEY}'}
    r = requests.get(url, headers=headers)
    return r.json()

# StockTwits
def get_stocktwits_messages(symbol='AAPL'):
    url = f'https://api.stocktwits.com/api/2/streams/symbol/{symbol}.json'
    r = requests.get(url)
    return r.json()

# Economic Calendar (مثال: استخدام موقع tradingeconomics)
TE_API_KEY = 'YOUR_TRADINGECONOMICS_KEY'
def get_economic_calendar(country='all', start_date=None, end_date=None):
    url = f'https://api.tradingeconomics.com/calendar/country/{country}?c={TE_API_KEY}'
    if start_date and end_date:
        url += f'&d1={start_date}&d2={end_date}'
    r = requests.get(url)
    return r.json()

# WebSocket مثال (Binance)
def binance_ws(symbol='btcusdt', on_message=None):
    ws_url = f'wss://stream.binance.com:9443/ws/{symbol}@trade'
    def on_message_default(ws, message):
        print('Binance WS:', message)
    ws = websocket.WebSocketApp(ws_url, on_message=on_message or on_message_default)
    ws.run_forever()

# Finnhub
FINNHUB_API_KEY = 'YOUR_FINNHUB_KEY'
def get_finnhub_quote(symbol='AAPL'):
    url = f'https://finnhub.io/api/v1/quote?symbol={symbol}&token={FINNHUB_API_KEY}'
    r = requests.get(url)
    return r.json()

def get_finnhub_news(category='general'):
    url = f'https://finnhub.io/api/v1/news?category={category}&token={FINNHUB_API_KEY}'
    r = requests.get(url)
    return r.json()

# Flask API bridge for Flutter
app = Flask(__name__)

# توحيد تنسيق OHLCV

def to_ohlcv_from_alpha_vantage(raw):
    ts = raw.get('Time Series (1min)', {})
    ohlcv = []
    for t, v in ts.items():
        ohlcv.append({
            'time': int(datetime.strptime(t, '%Y-%m-%d %H:%M:%S').timestamp()),
            'open': float(v['1. open']),
            'high': float(v['2. high']),
            'low': float(v['3. low']),
            'close': float(v['4. close']),
            'volume': float(v['5. volume'])
        })
    return sorted(ohlcv, key=lambda x: x['time'])

def to_ohlcv_from_coingecko(raw):
    ohlcv = []
    for row in raw:
        ohlcv.append({
            'time': int(row[0] / 1000),
            'open': row[1],
            'high': row[2],
            'low': row[3],
            'close': row[4],
            'volume': row[5] if len(row) > 5 else 0
        })
    return ohlcv

# SQLite Cache
CACHE_DB = 'data_cache.db'
def init_cache():
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS cache (key TEXT PRIMARY KEY, value TEXT, ts INTEGER)''')
    conn.commit()
    conn.close()
init_cache()

def get_cache(key, max_age_sec=60):
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('SELECT value, ts FROM cache WHERE key=?', (key,))
    row = c.fetchone()
    conn.close()
    if row:
        value, ts = row
        if int(time.time()) - ts < max_age_sec:
            return json.loads(value)
    return None

def set_cache(key, value):
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('REPLACE INTO cache (key, value, ts) VALUES (?, ?, ?)', (key, json.dumps(value), int(time.time())))
    conn.commit()
    conn.close()

# بيانات اقتصادية متقدمة
FRED_API_KEY = 'YOUR_FRED_KEY'
NEWSAPI_KEY = 'YOUR_NEWSAPI_KEY'
REDDIT_CLIENT_ID = 'YOUR_REDDIT_ID'
REDDIT_SECRET = 'YOUR_REDDIT_SECRET'
REDDIT_USER_AGENT = 'ai300app/0.1'
TWITTER_BEARER = 'YOUR_TWITTER_BEARER'

# أرباح الشركات (مثال: Alpha Vantage)
def get_earnings(symbol='AAPL'):
    url = f'https://www.alphavantage.co/query?function=EARNINGS&symbol={symbol}&apikey={ALPHA_VANTAGE_API_KEY}'
    r = requests.get(url)
    return r.json()

# أسعار الفائدة (مثال: FRED)
def get_interest_rate(series_id='FEDFUNDS'):
    url = f'https://api.stlouisfed.org/fred/series/observations?series_id={series_id}&api_key={FRED_API_KEY}&file_type=json'
    r = requests.get(url)
    return r.json()

# التضخم (CPI)
def get_inflation(series_id='CPIAUCSL'):
    url = f'https://api.stlouisfed.org/fred/series/observations?series_id={series_id}&api_key={FRED_API_KEY}&file_type=json'
    r = requests.get(url)
    return r.json()

# الناتج المحلي (GDP)
def get_gdp(series_id='GDP'):
    url = f'https://api.stlouisfed.org/fred/series/observations?series_id={series_id}&api_key={FRED_API_KEY}&file_type=json'
    r = requests.get(url)
    return r.json()

# الأخبار من NewsAPI
def get_newsapi(query='stock market', language='en'):
    url = f'https://newsapi.org/v2/everything?q={query}&language={language}&apiKey={NEWSAPI_KEY}'
    r = requests.get(url)
    return r.json()

# الأخبار من Reddit
def get_reddit_posts(subreddit='stocks', limit=10):
    headers = {'User-Agent': REDDIT_USER_AGENT}
    auth = requests.auth.HTTPBasicAuth(REDDIT_CLIENT_ID, REDDIT_SECRET)
    data = {'grant_type': 'client_credentials'}
    token = requests.post('https://www.reddit.com/api/v1/access_token', auth=auth, data=data, headers=headers).json()['access_token']
    headers['Authorization'] = f'bearer {token}'
    url = f'https://oauth.reddit.com/r/{subreddit}/hot?limit={limit}'
    r = requests.get(url, headers=headers)
    return r.json()

# الأخبار من X (Twitter)
def get_twitter(query='stock', max_results=10):
    url = f'https://api.twitter.com/2/tweets/search/recent?query={query}&max_results={max_results}'
    headers = {'Authorization': f'Bearer {TWITTER_BEARER}'}
    r = requests.get(url, headers=headers)
    return r.json()

# تحليل المشاعر (بسيط)
def analyze_sentiment(text):
    # يمكن استخدام مكتبة TextBlob أو Vader أو أي نموذج ML
    from textblob import TextBlob
    return TextBlob(text).sentiment.polarity

def to_ohlcv_from_oanda(raw):
    ohlcv = []
    for candle in raw.get('candles', []):
        ohlcv.append({
            'time': int(datetime.strptime(candle['time'][:19], '%Y-%m-%dT%H:%M:%S').timestamp()),
            'open': float(candle['mid']['o']),
            'high': float(candle['mid']['h']),
            'low': float(candle['mid']['l']),
            'close': float(candle['mid']['c']),
            'volume': float(candle['volume'])
        })
    return ohlcv

def to_ohlcv_from_finnhub(raw):
    ohlcv = []
    if 't' in raw and 'o' in raw:
        for i in range(len(raw['t'])):
            ohlcv.append({
                'time': int(raw['t'][i]),
                'open': raw['o'][i],
                'high': raw['h'][i],
                'low': raw['l'][i],
                'close': raw['c'][i],
                'volume': raw['v'][i] if 'v' in raw else 0
            })
    return ohlcv

def to_news_from_stocktwits(raw):
    news = []
    for msg in raw.get('messages', []):
        news.append({
            'id': msg['id'],
            'user': msg['user']['username'],
            'body': msg['body'],
            'created_at': msg['created_at']
        })
    return news

def to_events_from_economic(raw):
    events = []
    for event in raw:
        events.append({
            'event': event.get('event'),
            'country': event.get('country'),
            'date': event.get('date'),
            'actual': event.get('actual'),
            'forecast': event.get('forecast'),
            'previous': event.get('previous'),
            'impact': event.get('impact')
        })
    return events

# WebSocket Binance بث حي
binance_ws_queue = queue.Queue()
def binance_ws_flask(symbol='btcusdt'):
    def on_message(ws, message):
        binance_ws_queue.put(message)
    ws_url = f'wss://stream.binance.com:9443/ws/{symbol}@trade'
    ws = websocket.WebSocketApp(ws_url, on_message=on_message)
    threading.Thread(target=ws.run_forever, daemon=True).start()

def stream_binance():
    def event_stream():
        while True:
            msg = binance_ws_queue.get()
            yield f'data: {msg}\n\n'
    return Response(event_stream(), mimetype='text/event-stream')

@app.route('/ws/binance')
def ws_binance():
    symbol = request.args.get('symbol', 'btcusdt')
    binance_ws_flask(symbol)
    return stream_binance()

# تحديث fetch لدعم كل المصادر
@app.route('/fetch', methods=['POST'])
def fetch():
    data = request.json
    source = data.get('source')
    params = data.get('params', {})
    key = f'{source}:{json.dumps(params, sort_keys=True)}'
    cached = get_cache(key)
    if cached:
        return jsonify(cached)
    if source == 'earnings':
        result = get_earnings(params.get('symbol', 'AAPL'))
    elif source == 'interest_rate':
        result = get_interest_rate(params.get('series_id', 'FEDFUNDS'))
    elif source == 'inflation':
        result = get_inflation(params.get('series_id', 'CPIAUCSL'))
    elif source == 'gdp':
        result = get_gdp(params.get('series_id', 'GDP'))
    elif source == 'newsapi':
        result = get_newsapi(params.get('query', 'stock market'))
    elif source == 'reddit':
        result = get_reddit_posts(params.get('subreddit', 'stocks'))
    elif source == 'twitter':
        result = get_twitter(params.get('query', 'stock'))
    elif source == 'sentiment':
        result = {'sentiment': analyze_sentiment(params.get('text', ''))}
    elif source == 'alpha_vantage':
        raw = get_alpha_vantage(params.get('symbol', 'AAPL'), params.get('interval', '1min'))
        result = {'ohlcv': to_ohlcv_from_alpha_vantage(raw)}
    elif source == 'coingecko':
        raw = get_coingecko_ohlc(params.get('coin_id', 'bitcoin'), params.get('vs_currency', 'usd'))
        result = {'ohlcv': to_ohlcv_from_coingecko(raw)}
    elif source == 'oanda':
        raw = get_oanda_candles(params.get('instrument', 'EUR_USD'), params.get('granularity', 'M1'))
        result = {'ohlcv': to_ohlcv_from_oanda(raw)}
    elif source == 'stocktwits':
        raw = get_stocktwits_messages(params.get('symbol', 'AAPL'))
        result = {'news': to_news_from_stocktwits(raw)}
    elif source == 'economic':
        raw = get_economic_calendar(params.get('country', 'all'))
        result = {'events': to_events_from_economic(raw)}
    elif source == 'finnhub':
        raw = get_finnhub_quote(params.get('symbol', 'AAPL'))
        result = {'ohlcv': to_ohlcv_from_finnhub(raw)}
    elif source == 'finnhub_news':
        return jsonify(get_finnhub_news(params.get('category', 'general')))
    else:
        result = {'error': 'Unknown source'}
    set_cache(key, result)
    return jsonify(result)

# fallback للنسخة القديمة من fetch (للمصادر الأخرى)
def old_fetch():
    # ... نسخة من دالة fetch السابقة للمصادر الأخرى ...
    pass

@app.route('/predict/model', methods=['POST'])
def predict_model():
    data = request.json
    model_type = data.get('model_type')
    input_data = data.get('input', [])
    result = None
    try:
        if model_type == 'llama3':
            from transformers import AutoTokenizer, AutoModelForCausalLM
            tokenizer = AutoTokenizer.from_pretrained('meta-llama/Meta-Llama-3-8B')
            model = AutoModelForCausalLM.from_pretrained('meta-llama/Meta-Llama-3-8B')
            prompt = data.get('prompt', 'Hello')
            inputs = tokenizer(prompt, return_tensors='pt')
            outputs = model.generate(**inputs, max_new_tokens=64)
            result = tokenizer.decode(outputs[0], skip_special_tokens=True)
        elif model_type == 'huggingface':
            from transformers import pipeline
            task = data.get('task', 'text-classification')
            pipe = pipeline(task)
            result = pipe(data.get('text', 'Hello'))
        elif model_type == 'geometric':
            import torch_geometric
            # مثال: تصنيف عقدة باستخدام GCN
            from torch_geometric.nn import GCNConv
            # نموذج وهمي (يجب تخصيصه حسب التطبيق)
            x = torch.tensor(input_data, dtype=torch.float)
            edge_index = torch.tensor(data.get('edge_index', [[0,1],[1,2]]), dtype=torch.long)
            class Net(torch.nn.Module):
                def __init__(self):
                    super().__init__()
                    self.conv1 = GCNConv(x.size(1), 16)
                    self.conv2 = GCNConv(16, 2)
                def forward(self, x, edge_index):
                    x = self.conv1(x, edge_index)
                    x = torch.relu(x)
                    x = self.conv2(x, edge_index)
                    return x
            net = Net()
            out = net(x, edge_index)
            result = out.detach().numpy().tolist()
        elif model_type == 'deepspeed':
            import deepspeed
            # مثال: تشغيل نموذج كبير مع DeepSpeed (يجب تخصيصه حسب التطبيق)
            # ... إعداد النموذج ...
            result = 'DeepSpeed model inference executed.'
        elif model_type == 'sb3':
            from stable_baselines3 import PPO
            import gym
            env = gym.make(data.get('env', 'CartPole-v1'))
            model = PPO.load(data.get('model_path', 'sb3_model.zip'))
            obs = env.reset()
            action, _ = model.predict(obs)
            result = int(action)
        elif model_type == 'river':
            import river
            from river import linear_model, preprocessing
            model = preprocessing.StandardScaler() | linear_model.LogisticRegression()
            for x in input_data:
                model = model.learn_one(x['features'], x['label'])
            y_pred = model.predict_one(input_data[-1]['features'])
            result = y_pred
        elif model_type == 'onnx':
            import onnxruntime as ort
            sess = ort.InferenceSession(data.get('model_path', 'assets/models/onnx_model.onnx'))
            input_name = sess.get_inputs()[0].name
            arr = np.array(input_data, dtype=np.float32)
            pred = sess.run(None, {input_name: arr})
            result = pred[0].tolist()
        else:
            result = {'error': 'Unknown or unsupported model_type'}
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    return jsonify({'result': result})

if __name__ == '__main__':
    threading.Thread(target=lambda: app.run(host='0.0.0.0', port=5005, debug=False), daemon=True).start()
    # يمكن هنا تشغيل WebSocket أو أي مهام أخرى
    while True:
        time.sleep(1) 