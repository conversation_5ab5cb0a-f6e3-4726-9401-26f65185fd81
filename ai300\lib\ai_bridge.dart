import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

class AIBridge {
  static const platform = MethodChannel('ai300.channel');

  /// يرسل بيانات الإغلاق إلى سكريبت بايثون ويعيد التوقع
  static Future<double?> predictLSTM(List<double> closes) async {
    try {
      final process = await Process.start('python', ['predict_lstm.py'], runInShell: true);
      process.stdin.writeln(closes.join(','));
      await process.stdin.close();
      final output = await process.stdout.transform(SystemEncoding().decoder).join();
      return double.tryParse(output.trim());
    } catch (e) {
      print('AI Error: $e');
      return null;
    }
  }

  static Future<String?> detectPattern(List<double> closes) async {
    try {
      final process = await Process.start('python', ['patterns.py'], runInShell: true);
      process.stdin.writeln(closes.join(','));
      await process.stdin.close();
      final output = await process.stdout.transform(SystemEncoding().decoder).join();
      return output.trim();
    } catch (e) {
      print('Pattern Error: $e');
      return null;
    }
  }

  static Future<Map<String, dynamic>> getTAIndicators(
    List<double> open,
    List<double> high,
    List<double> low,
    List<double> close,
    List<double> volume,
  ) async {
    final url = Uri.parse('http://localhost:5001/ta');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'open': open,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume,
      }),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('TA-Lib API error: ${response.body}');
    }
  }

  static Future<Map<String, double>> getLstmShapImportances() async {
    final result = await platform.invokeMethod('getLstmShapImportances');
    // يفترض أن النتيجة عبارة عن Map<String, double>
    return Map<String, double>.from(result);
  }

  static Future<Map<String, dynamic>?> analyzeFinancialText(String text) async {
    try {
      final process = await Process.start('python', ['models/language_model.py'], runInShell: true);
      process.stdin.writeln(text);
      await process.stdin.close();
      final output = await process.stdout.transform(SystemEncoding().decoder).join();
      return jsonDecode(output.replaceSingleQuotes());
    } catch (e) {
      print('LanguageModel Error: $e');
      return null;
    }
  }

  static Future<String?> detectPatternML(List<double> closes) async {
    try {
      final url = Uri.parse('http://localhost:5002/detect_pattern');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'close': closes}),
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['pattern'] as String?;
      } else {
        return null;
      }
    } catch (e) {
      print('Pattern ML Error: $e');
      return null;
    }
  }
}

// دالة مساعدة لتحويل single quotes إلى double quotes في JSON
extension _StringJsonFix on String {
  String replaceSingleQuotes() => replaceAll("'", '"');
} 