import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'dart:developer' as developer;
import 'dart:io';
import 'dart:async';

class LoggingMonitoringService {
  static Future<void> init() async {
    await SentryFlutter.init(
      (options) {
        options.dsn = 'YOUR_SENTRY_DSN';
        options.tracesSampleRate = 1.0;
      },
    );
    await DatadogSdk.instance.initialize(
      clientToken: 'YOUR_DATADOG_CLIENT_TOKEN',
      env: 'production',
      serviceName: 'ai_300_app',
      trackingConsent: TrackingConsent.granted,
      site: DatadogSite.eu1,
      rumConfiguration: DatadogRumConfiguration(
        applicationId: 'YOUR_DATADOG_APP_ID',
        sessionSampleRate: 100,
        telemetrySampleRate: 100,
        userActionTracking: true,
        longTaskTracking: true,
        viewTrackingStrategy: DatadogNavigationObserver(),
      ),
      loggingConfiguration: DatadogLoggingConfiguration(),
      traceConfiguration: DatadogTraceConfiguration(),
    );
  }

  static void logError(dynamic error, {StackTrace? stackTrace}) {
    Sentry.captureException(error, stackTrace: stackTrace);
    DatadogSdk.instance.logs?.info('Error: $error', error: error, stackTrace: stackTrace);
  }

  static void logEvent(String message) {
    Sentry.captureMessage(message);
    DatadogSdk.instance.logs?.info(message);
  }

  static void logPerformance(String name, Duration duration) {
    DatadogSdk.instance.rum?.addTiming(name);
    developer.log('Performance: $name - ${duration.inMilliseconds}ms');
  }

  static Future<Map<String, dynamic>> getSystemMetrics() async {
    // مراقبة مؤشرات الأداء (CPU, Memory, Latency)
    final mem = ProcessInfo.currentRss;
    // ملاحظة: Dart لا توفر مراقبة CPU مباشرة، يمكن استخدام حزم خارجية أو مراقبة latency يدوياً
    return {
      'memory': mem,
      'cpu': 'N/A',
      'latency': 'N/A',
    };
  }
} 