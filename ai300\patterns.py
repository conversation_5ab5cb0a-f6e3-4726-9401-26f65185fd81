import sys
import numpy as np

def detect_head_and_shoulders(closes):
    if len(closes) < 7:
        return False
    for i in range(2, len(closes) - 3):
        if closes[i-2] < closes[i-1] < closes[i] > closes[i+1] > closes[i+2] < closes[i+3]:
            return True
    return False

def detect_inverse_head_and_shoulders(closes):
    if len(closes) < 7:
        return False
    for i in range(2, len(closes) - 3):
        if closes[i-2] > closes[i-1] > closes[i] < closes[i+1] < closes[i+2] > closes[i+3]:
            return True
    return False

def detect_rising_wedge(closes):
    if len(closes) < 6:
        return False
    x = np.arange(len(closes))
    z = np.polyfit(x, closes, 1)
    return z[0] > 0 and (max(closes) - min(closes)) < (sum(closes) / len(closes)) * 0.15

def detect_falling_wedge(closes):
    if len(closes) < 6:
        return False
    x = np.arange(len(closes))
    z = np.polyfit(x, closes, 1)
    return z[0] < 0 and (max(closes) - min(closes)) < (sum(closes) / len(closes)) * 0.15

def detect_triangle(closes):
    if len(closes) < 6:
        return False
    window = closes[-6:]
    diff = max(window) - min(window)
    avg = sum(window) / len(window)
    if diff < avg * 0.05:
        return 'Symmetrical Triangle'
    elif window[0] < window[-1]:
        return 'Ascending Triangle'
    elif window[0] > window[-1]:
        return 'Descending Triangle'
    return False

def detect_flag(closes):
    if len(closes) < 8:
        return False
    # مبسط: حركة قوية ثم قناة ضيقة
    if abs(closes[-8] - closes[-6]) > (sum(closes[-8:-6]) / 2) * 0.03:
        channel = max(closes[-5:]) - min(closes[-5:])
        avg = sum(closes[-5:]) / 5
        if channel < avg * 0.02:
            return True
    return False

def detect_pennant(closes):
    if len(closes) < 8:
        return False
    # مبسط: حركة قوية ثم تذبذب متناقص
    if abs(closes[-8] - closes[-6]) > (sum(closes[-8:-6]) / 2) * 0.03:
        window = closes[-5:]
        if max(window) - min(window) < (sum(window) / len(window)) * 0.015:
            return True
    return False

def detect_cup_and_handle(closes):
    if len(closes) < 15:
        return False
    mid = len(closes) // 2
    left = closes[:mid]
    right = closes[mid:]
    if min(left) == left[-1] and min(right) == right[0]:
        if max(closes) - min(closes) > (sum(closes) / len(closes)) * 0.1:
            return True
    return False

def main():
    input_data = sys.stdin.read().strip()
    closes = [float(x) for x in input_data.split(',') if x]
    if detect_head_and_shoulders(closes):
        print('Head & Shoulders')
    elif detect_inverse_head_and_shoulders(closes):
        print('Inverse Head & Shoulders')
    elif detect_rising_wedge(closes):
        print('Rising Wedge')
    elif detect_falling_wedge(closes):
        print('Falling Wedge')
    else:
        tri = detect_triangle(closes)
        if tri:
            print(tri)
        elif detect_flag(closes):
            print('Flag')
        elif detect_pennant(closes):
            print('Pennant')
        elif detect_cup_and_handle(closes):
            print('Cup & Handle')
        else:
            print('None')

if __name__ == '__main__':
    main() 