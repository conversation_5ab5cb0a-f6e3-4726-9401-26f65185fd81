import 'dart:convert';
import 'package:http/http.dart' as http;

class TradingBridge {
  static Future<Map<String, dynamic>?> tradeBinance({
    required String apiKey,
    required String apiSecret,
    required String symbol,
    required String side,
    required String type,
    required double amount,
    double? price,
  }) async {
    final url = Uri.parse('http://localhost:5007/trade_binance');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'api_key': apiKey,
        'api_secret': apiSecret,
        'symbol': symbol,
        'side': side,
        'type': type,
        'amount': amount,
        if (price != null) 'price': price,
      }),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }

  static Future<Map<String, dynamic>?> tradeMT5({
    required String symbol,
    required String side,
    required String type,
    required double amount,
    double? price,
    required int login,
    required String password,
    required String server,
  }) async {
    final url = Uri.parse('http://localhost:5007/trade_mt5');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'symbol': symbol,
        'side': side,
        'type': type,
        'amount': amount,
        if (price != null) 'price': price,
        'login': login,
        'password': password,
        'server': server,
      }),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }

  static Future<Map<String, dynamic>?> tradeIB({
    required String symbol,
    required String side,
    required String type,
    required double amount,
    double? price,
  }) async {
    final url = Uri.parse('http://localhost:5007/trade_ib');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'symbol': symbol,
        'side': side,
        'type': type,
        'amount': amount,
        if (price != null) 'price': price,
      }),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }

  static Future<Map<String, dynamic>?> riskManagement({
    required double balance,
    double riskPerTrade = 0.01,
    double winRate = 0.5,
    double winLossRatio = 2,
  }) async {
    final url = Uri.parse('http://localhost:5007/risk_management');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'balance': balance,
        'risk_per_trade': riskPerTrade,
        'win_rate': winRate,
        'win_loss_ratio': winLossRatio,
      }),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {'error': response.body};
    }
  }

  static Future<List<dynamic>?> getOrderLog() async {
    final url = Uri.parse('http://localhost:5007/order_log');
    final response = await http.get(url);
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['orders'] as List<dynamic>;
    } else {
      return null;
    }
  }
} 