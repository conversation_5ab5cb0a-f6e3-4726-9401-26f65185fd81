from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np
import pandas as pd
import scipy.signal

app = Flask(__name__)
CORS(app)

def gann_angles(prices):
    # حساب زوايا Gann (مبسطة)
    n = len(prices)
    x = np.arange(n)
    y = np.array(prices)
    angles = []
    for deg in [45, 26.5, 63.75, 18.75, 75, 15]:
        slope = np.tan(np.radians(deg))
        line = y[0] + slope * (x - x[0])
        angles.append(line.tolist())
    return angles

def detect_cycles(prices):
    # كشف الدورات الزمنية باستخدام تحويل فورييه السريع FFT
    y = np.array(prices)
    fft = np.fft.fft(y - np.mean(y))
    freqs = np.fft.fftfreq(len(y))
    idx = np.argsort(np.abs(fft))[::-1]
    main_cycles = []
    for i in idx[1:6]:
        if freqs[i] != 0:
            period = abs(1 / freqs[i])
            if 2 < period < len(y) // 2:
                main_cycles.append(period)
    return main_cycles

def turning_points(prices):
    # نقاط التحول (قمم وقيعان)
    y = np.array(prices)
    peaks, _ = scipy.signal.find_peaks(y)
    troughs, _ = scipy.signal.find_peaks(-y)
    return {'peaks': peaks.tolist(), 'troughs': troughs.tolist()}

@app.route('/gann_cycles', methods=['POST'])
def gann_cycles():
    data = request.json
    closes = data['close']
    angles = gann_angles(closes)
    cycles = detect_cycles(closes)
    turns = turning_points(closes)
    return jsonify({'gann_angles': angles, 'cycles': cycles, 'turning_points': turns})

if __name__ == '__main__':
    app.run(port=5004, debug=True) 