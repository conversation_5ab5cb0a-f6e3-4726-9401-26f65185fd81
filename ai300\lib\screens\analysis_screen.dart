import 'package:flutter/material.dart';
import '../ai_models.dart';
import '../ai_bridge.dart';
import '../wear_service.dart';
import 'charts_screen.dart';
import '../models/data_provider.dart';

class AnalysisScreen extends StatefulWidget {
  const AnalysisScreen({Key? key}) : super(key: key);
  @override
  State<AnalysisScreen> createState() => _AnalysisScreenState();
}

class _AnalysisScreenState extends State<AnalysisScreen> {
  Map<ModelType, double?> predictions = {};
  Map<ModelType, double?> confidences = {};
  Map<ModelType, String> confidenceDetails = {};
  bool loading = true;
  String? xaiExplanation;
  String? error;
  List<double> sampleInput = List.generate(30, (i) => 30000 + i * 10 + (i % 2 == 0 ? 50 : -50)); // بيانات وهمية
  String? textAnalysisResult;
  String? textSentimentLabel;
  double? textSentimentScore;
  TextEditingController textController = TextEditingController();
  Map<String, Map<String, double?>> assetPredictions = {};
  Map<String, Map<String, double?>> assetConfidences = {};
  final List<String> assets = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'];
  final List<String> horizons = ['قصير', 'متوسط', 'طويل'];
  Map<String, dynamic>? yahooSummary;
  Map<String, dynamic>? alphaOverview;
  List<dynamic>? economicEvents;
  String? advancedModelResults;

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  Future<void> fetchData() async {
    setState(() { loading = true; error = null; });
    try {
      yahooSummary = await DataProvider.getYahooFinanceSummary('AAPL');
      alphaOverview = await DataProvider.getAlphaVantageOverview('AAPL', 'demo');
      economicEvents = await DataProvider.getEconomicEvents(country: 'us');
      setState(() { loading = false; });
    } catch (e) {
      setState(() { error = e.toString(); loading = false; });
    }
  }

  double calcRMSE(List<double> preds, List<double> actuals) {
    double mse = 0;
    for (int i = 0; i < preds.length; i++) {
      mse += (preds[i] - actuals[i]) * (preds[i] - actuals[i]);
    }
    mse /= preds.length;
    return mse.sqrt();
  }

  double calcStd(List<double> values) {
    double mean = values.reduce((a, b) => a + b) / values.length;
    double sum = values.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b);
    return (sum / values.length).sqrt();
  }

  Future<void> runAllModels(List<double> input) async {
    setState(() { loading = true; error = null; });
    try {
      for (final type in ModelType.values) {
        AIModelManager.selectModel(type);
        final pred = await AIModelManager.predict(input);
        predictions[type] = pred;
        // حساب الثقة المتقدم
        double std = calcStd([pred, ...input.sublist(input.length-5)]);
        double rmse = calcRMSE([pred], [input.last]);
        double conf = 1.0 - (rmse / (input.last.abs() + 1e-6)) - (std / (input.last.abs() + 1e-6)) * 0.5;
        conf = conf.clamp(0, 1);
        confidences[type] = conf;
        confidenceDetails[type] = 'RMSE: ${rmse.toStringAsFixed(2)}, Std: ${std.toStringAsFixed(2)}';
      }
      // شرح XAI مفصل
      xaiExplanation = 'العوامل المؤثرة:\n';
      xaiExplanation! += '- آخر تغير: ${(input[input.length-1] - input[input.length-2]).toStringAsFixed(2)}\n';
      xaiExplanation! += '- متوسط آخر 5 أسعار: ${(input.sublist(input.length-5).reduce((a,b)=>a+b)/5).toStringAsFixed(2)}\n';
      xaiExplanation! += '- الانحراف المعياري: ${(calcStd(input.sublist(input.length-5))).toStringAsFixed(2)}';
    } catch (e) {
      error = e.toString();
    }
    setState(() { loading = false; });
  }

  void showXaiDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شرح XAI مفصل'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('العوامل المؤثرة في التوصية:', style: TextStyle(fontWeight: FontWeight.bold)),
              if (xaiExplanation != null) Text(xaiExplanation!),
              const SizedBox(height: 12),
              const Text('تفاصيل الثقة لكل نموذج:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...ModelType.values.map((type) => Text('${type.name.toUpperCase()}: ${confidenceDetails[type] ?? '-'}')),
            ],
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('إغلاق'))],
      ),
    );
  }

  Color confidenceColor(double? conf) {
    if (conf == null) return Colors.grey;
    if (conf > 0.8) return Colors.green;
    if (conf > 0.6) return Colors.orange;
    return Colors.red;
  }

  Future<void> analyzeText() async {
    setState(() { textAnalysisResult = null; textSentimentLabel = null; textSentimentScore = null; });
    final result = await AIBridge.analyzeFinancialText(textController.text);
    if (result != null && result['label'] != null && result['score'] != null) {
      setState(() {
        textSentimentLabel = result['label'].toString().toUpperCase();
        textSentimentScore = double.tryParse(result['score'].toString());
        textAnalysisResult = '${result['label']} (الثقة: ${(textSentimentScore!*100).toStringAsFixed(1)}%)';
      });
      // إشعار تداول إذا كانت النتيجة قوية
      if (textSentimentScore != null && textSentimentScore! > 0.8) {
        if (textSentimentLabel == 'POSITIVE') {
          WearService.sendWearNotification('خبر إيجابي', 'تحليل AI: خبر إيجابي (ثقة ${(textSentimentScore!*100).toStringAsFixed(1)}%)');
        } else if (textSentimentLabel == 'NEGATIVE') {
          WearService.sendWearNotification('خبر سلبي', 'تحليل AI: خبر سلبي (ثقة ${(textSentimentScore!*100).toStringAsFixed(1)}%)');
        }
      }
    } else {
      setState(() { textAnalysisResult = result != null ? result.toString() : 'تعذر التحليل'; });
    }
  }

  Color getSentimentColor() {
    if (textSentimentLabel == 'POSITIVE') return Colors.green[100]!;
    if (textSentimentLabel == 'NEGATIVE') return Colors.red[100]!;
    if (textSentimentLabel == 'NEUTRAL') return Colors.grey[200]!;
    return Colors.blue[50]!;
  }

  IconData getSentimentIcon() {
    if (textSentimentLabel == 'POSITIVE') return Icons.trending_up;
    if (textSentimentLabel == 'NEGATIVE') return Icons.trending_down;
    if (textSentimentLabel == 'NEUTRAL') return Icons.trending_flat;
    return Icons.analytics;
  }

  void showChartForAsset(String asset, String horizon) {
    final pred = assetPredictions[asset]?[horizon];
    final conf = assetConfidences[asset]?[horizon];
    Navigator.of(context).push(MaterialPageRoute(
      builder: (_) => ChartsScreen(
        aiRecommendation: pred != null ? (pred > 0 ? 'صعود' : pred < 0 ? 'هبوط' : 'ثبات') : null,
        aiConfidence: conf,
      ),
    ));
  }

  // إشعار تلقائي عند توصية قوية لأي أصل وزمن
  void checkAssetAlerts() {
    for (final asset in assets) {
      for (final h in horizons) {
        final conf = assetConfidences[asset]?[h];
        final pred = assetPredictions[asset]?[h];
        if (conf != null && conf > 0.8 && pred != null) {
          final dir = pred > 0 ? 'صعود' : pred < 0 ? 'هبوط' : 'ثبات';
          WearService.sendWearNotification('توصية قوية', 'توصية $dir على $asset ($h) بثقة ${(conf * 100).toStringAsFixed(1)}%');
        }
      }
    }
  }

  // استدعاء checkAssetAlerts بعد تشغيل التوصيات
  Future<void> runMultiAssetModels() async {
    setState(() { assetPredictions.clear(); assetConfidences.clear(); });
    for (final asset in assets) {
      assetPredictions[asset] = {};
      assetConfidences[asset] = {};
      // جلب بيانات وهمية لكل أصل (في التطبيق الفعلي: جلب بيانات حقيقية)
      List<double> closes = List.generate(60, (i) => 30000 + i * 10 + (i % 2 == 0 ? 50 : -50) + (asset == 'ETHUSDT' ? 1000 : asset == 'BNBUSDT' ? 500 : 0));
      for (final horizon in horizons) {
        List<double> input;
        if (horizon == 'قصير') {
          input = closes.sublist(closes.length - 10);
        } else if (horizon == 'متوسط') {
          input = closes.sublist(closes.length - 30);
        } else {
          input = closes.sublist(closes.length - 60);
        }
        AIModelManager.selectModel(ModelType.lstm); // يمكن تخصيص النموذج لكل أصل/زمن
        final pred = await AIModelManager.predict(input);
        double conf = 1.0 - ((pred - input.last).abs() / (input.last.abs() + 1e-6));
        assetPredictions[asset]![horizon] = pred;
        assetConfidences[asset]![horizon] = conf;
      }
    }
    setState(() {});
    checkAssetAlerts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('التحليل الأساسي والاقتصادي')),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(child: Text('خطأ: $error'))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (yahooSummary != null) ...[
                        const Text('Yahoo Finance:', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('سعر السهم: ${yahooSummary!['price']?['regularMarketPrice']?['raw'] ?? '-'}'),
                        Text('العائد السنوي: ${yahooSummary!['summaryDetail']?['dividendYield']?['raw'] ?? '-'}'),
                        Text('نسبة الفائدة: ${yahooSummary!['financialData']?['interestCoverage']?['raw'] ?? '-'}'),
                        Text('الناتج المحلي (تقديري): ${yahooSummary!['defaultKeyStatistics']?['enterpriseToRevenue']?['raw'] ?? '-'}'),
                        const SizedBox(height: 12),
                        const Text('المصدر: Yahoo Finance', style: TextStyle(fontSize: 12, color: Colors.grey)),
                      ],
                      if (alphaOverview != null) ...[
                        const Divider(),
                        const Text('Alpha Vantage:', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('Market Cap: ${alphaOverview!['MarketCapitalization'] ?? '-'}'),
                        Text('PERatio: ${alphaOverview!['PERatio'] ?? '-'}'),
                        Text('EPS: ${alphaOverview!['EPS'] ?? '-'}'),
                        Text('DividendYield: ${alphaOverview!['DividendYield'] ?? '-'}'),
                        const SizedBox(height: 12),
                        const Text('المصدر: Alpha Vantage', style: TextStyle(fontSize: 12, color: Colors.grey)),
                      ],
                      if (economicEvents != null) ...[
                        const Divider(),
                        const Text('Economic Calendar:', style: TextStyle(fontWeight: FontWeight.bold)),
                        ...economicEvents!.take(5).map((e) => Text('${e['title']} - ${e['country']} - ${e['date']}')).toList(),
                        const SizedBox(height: 12),
                        const Text('المصدر: Economic Calendar API', style: TextStyle(fontSize: 12, color: Colors.grey)),
                      ],
                      ElevatedButton(
                        onPressed: () async {
                          setState(() { loading = true; });
                          final input = List<double>.generate(30, (i) => 100 + i.toDouble());
                          final lstm = await AIModelManager._predictTFLite('lstm_model.tflite', input);
                          final cnn = await AIModelManager._predictTFLite('cnn_model.tflite', input);
                          double? transformer, rl, automl, onnx, river, deepspeed, huggingface, llama3, geometric, sb3;
                          try { transformer = await AIModelManager.predict(input..[0]=101); AIModelManager.selectModel(ModelType.transformer); } catch (_) {}
                          try { rl = await AIModelManager.predict(input..[0]=102); AIModelManager.selectModel(ModelType.rl); } catch (_) {}
                          try { automl = await AIModelManager.predict(input..[0]=103); AIModelManager.selectModel(ModelType.automl); } catch (_) {}
                          try { onnx = await AIModelManager.predict(input..[0]=104); AIModelManager.selectModel(ModelType.onnx); } catch (_) {}
                          try { river = await AIModelManager.predict(input..[0]=105); AIModelManager.selectModel(ModelType.river); } catch (_) {}
                          try { deepspeed = await AIModelManager.predict(input..[0]=106); AIModelManager.selectModel(ModelType.deepspeed); } catch (_) {}
                          try { huggingface = await AIModelManager.predict(input..[0]=107); AIModelManager.selectModel(ModelType.huggingface); } catch (_) {}
                          try { llama3 = await AIModelManager.predict(input..[0]=108); AIModelManager.selectModel(ModelType.llama3); } catch (_) {}
                          try { geometric = await AIModelManager.predict(input..[0]=109); AIModelManager.selectModel(ModelType.geometric); } catch (_) {}
                          try { sb3 = await AIModelManager.predict(input..[0]=110); AIModelManager.selectModel(ModelType.sb3); } catch (_) {}
                          setState(() {
                            loading = false;
                            error = null;
                            xaiExplanation = 'LSTM: ' + lstm.toString() + '\nCNN: ' + cnn.toString();
                            advancedModelResults = 'TransformerXL: ${transformer ?? 'N/A'}\nRL: ${rl ?? 'N/A'}\nAutoML: ${automl ?? 'N/A'}\nONNX: ${onnx ?? 'N/A'}\nRiver: ${river ?? 'N/A'}\nDeepSpeed: ${deepspeed ?? 'N/A'}\nHuggingFace: ${huggingface ?? 'N/A'}\nLLaMA3: ${llama3 ?? 'N/A'}\nGeometric: ${geometric ?? 'N/A'}\nSB3: ${sb3 ?? 'N/A'}';
                          });
                        },
                        child: const Text('اختبار جميع النماذج المتقدمة'),
                      ),
                      if (advancedModelResults != null)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(advancedModelResults!, style: const TextStyle(color: Colors.deepPurple)),
                        ),
                    ],
                  ),
                ),
    );
  }
} 