import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:convert';
import 'dart:io';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

class PortfolioAnalysisScreen extends StatefulWidget {
  const PortfolioAnalysisScreen({super.key});
  @override
  State<PortfolioAnalysisScreen> createState() => _PortfolioAnalysisScreenState();
}

class _PortfolioAnalysisScreenState extends State<PortfolioAnalysisScreen> {
  List<double> returns = List.generate(252, (i) => Random().nextDouble() * 0.02 - 0.01); // بيانات وهمية
  double equity = 10000;
  double riskPerTrade = 0.02;
  double winRate = 0.55;
  double winLossRatio = 1.5;
  double? sharpe, sortino, calmar, fixedFractional, kelly;
  List<List<double>>? monteCarloPaths;
  int nSim = 20;
  int horizon = 100;
  Color mcColor = Colors.blue;
  bool loading = false;

  Future<void> analyzeRisk() async {
    setState(() { loading = true; });
    final process = await Process.start('python', ['models/risk_management.py'], runInShell: true);
    await process.stdin.close();
    final output = await process.stdout.transform(utf8.decoder).join();
    sharpe = double.tryParse(RegExp(r'Sharpe: ([\d\.-]+)').firstMatch(output)?.group(1) ?? '');
    sortino = double.tryParse(RegExp(r'Sortino: ([\d\.-]+)').firstMatch(output)?.group(1) ?? '');
    calmar = double.tryParse(RegExp(r'Calmar: ([\d\.-]+)').firstMatch(output)?.group(1) ?? '');
    // Monte Carlo - توليد عدة مسارات محلياً
    monteCarloPaths = List.generate(nSim, (_) {
      double eq = equity;
      List<double> path = [eq];
      for (int i = 0; i < horizon; i++) {
        double r = returns[Random().nextInt(returns.length)];
        eq *= (1 + r);
        path.add(eq);
      }
      return path;
    });
    fixedFractional = equity * riskPerTrade;
    kelly = winRate - (1 - winRate) / winLossRatio;
    setState(() { loading = false; });
  }

  void showCustomizeDialog() {
    int tempNSim = nSim;
    int tempHorizon = horizon;
    Color tempColor = mcColor;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تخصيص محاكاة Monte Carlo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                const Text('عدد المسارات:'),
                Expanded(
                  child: Slider(
                    value: tempNSim.toDouble(),
                    min: 5,
                    max: 100,
                    divisions: 19,
                    label: tempNSim.toString(),
                    onChanged: (v) => setState(() => tempNSim = v.round()),
                  ),
                ),
                Text(tempNSim.toString()),
              ],
            ),
            Row(
              children: [
                const Text('الأفق الزمني:'),
                Expanded(
                  child: Slider(
                    value: tempHorizon.toDouble(),
                    min: 20,
                    max: 300,
                    divisions: 14,
                    label: tempHorizon.toString(),
                    onChanged: (v) => setState(() => tempHorizon = v.round()),
                  ),
                ),
                Text(tempHorizon.toString()),
              ],
            ),
            Row(
              children: [
                const Text('لون المسارات:'),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () async {
                    Color? picked = await showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('اختر اللون'),
                        content: SingleChildScrollView(
                          child: BlockPicker(
                            pickerColor: tempColor,
                            onColorChanged: (c) => Navigator.of(context).pop(c),
                          ),
                        ),
                      ),
                    );
                    if (picked != null) setState(() => tempColor = picked);
                  },
                  child: CircleAvatar(backgroundColor: tempColor, radius: 14),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                nSim = tempNSim;
                horizon = tempHorizon;
                mcColor = tempColor;
              });
              analyzeRisk();
              Navigator.of(context).pop();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  Future<void> saveChartAsImage() async {
    // TODO: استخدم ScreenshotController أو RenderRepaintBoundary لحفظ الصورة
  }

  @override
  void initState() {
    super.initState();
    analyzeRisk();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تحليل وإدارة المخاطر')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: loading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text('مؤشرات الأداء والمخاطرة:', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Tooltip(
                          message: 'Sharpe: يقيس العائد المعدل بالمخاطرة\nSortino: يقيس العائد مقابل المخاطرة السلبية\nCalmar: يقيس العائد مقابل أكبر تراجع',
                          child: const Icon(Icons.info_outline, size: 18),
                        ),
                      ],
                    ),
                    Text('Sharpe Ratio: ${sharpe?.toStringAsFixed(3) ?? '-'}'),
                    Text('Sortino Ratio: ${sortino?.toStringAsFixed(3) ?? '-'}'),
                    Text('Calmar Ratio: ${calmar?.toStringAsFixed(3) ?? '-'}'),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('اقتراح حجم الصفقة:', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Tooltip(
                          message: 'Fixed Fractional: نسبة ثابتة من رأس المال\nKelly: تعظيم النمو على المدى الطويل',
                          child: const Icon(Icons.info_outline, size: 18),
                        ),
                      ],
                    ),
                    Text('Fixed Fractional (2%): ${fixedFractional?.toStringAsFixed(2) ?? '-'}'),
                    Text('Kelly Criterion: ${(kelly != null && kelly! > 0) ? (kelly! * 100).toStringAsFixed(2) + '%' : '0%'}'),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('محاكاة Monte Carlo (مسارات رأس المال):', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 8),
                        Tooltip(
                          message: 'محاكاة احتمالية لمسارات رأس المال المستقبلية بناءً على التوزيع التاريخي للعوائد',
                          child: const Icon(Icons.info_outline, size: 18),
                        ),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.tune),
                          tooltip: 'تخصيص',
                          onPressed: showCustomizeDialog,
                        ),
                        IconButton(
                          icon: const Icon(Icons.camera_alt),
                          tooltip: 'حفظ كصورة',
                          onPressed: saveChartAsImage,
                        ),
                      ],
                    ),
                    if (monteCarloPaths != null)
                      SizedBox(
                        height: 240,
                        child: LineChart(
                          LineChartData(
                            minY: monteCarloPaths!.expand((e) => e).reduce(min),
                            maxY: monteCarloPaths!.expand((e) => e).reduce(max),
                            lineBarsData: [
                              for (final path in monteCarloPaths!)
                                LineChartBarData(
                                  spots: [for (int i = 0; i < path.length; i++) FlSpot(i.toDouble(), path[i])],
                                  isCurved: false,
                                  color: mcColor.withOpacity(0.5 + 0.5 * Random().nextDouble()),
                                  dotData: FlDotData(show: false),
                                  barWidth: 1.5,
                                ),
                            ],
                          ),
                        ),
                      ),
                    if (monteCarloPaths == null)
                      const Text('لا توجد بيانات Monte Carlo.'),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة التحليل'),
                      onPressed: analyzeRisk,
                    ),
                  ],
                ),
              ),
      ),
    );
  }
} 