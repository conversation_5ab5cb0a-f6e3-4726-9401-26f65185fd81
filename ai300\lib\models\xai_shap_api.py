from flask import Flask, request, jsonify
from flask_cors import CORS
import torch
import shap
import numpy as np
from hybrid_models import LSTMModel, CNNModel, SimpleTransformerXL

app = Flask(__name__)
CORS(app)

# تحميل النماذج
lstm = torch.jit.load('lstm_model.pt')
cnn = torch.jit.load('cnn_model.pt')
txl = torch.jit.load('transformerxl_model.pt')

model_map = {
    'lstm': lstm,
    'cnn': cnn,
    'transformer': txl
}

@app.route('/xai_shap', methods=['POST'])
def xai_shap():
    data = request.json
    closes = np.array(data['close'], dtype=np.float32).reshape(1, -1, 1)
    model_type = data.get('model', 'lstm')
    model = model_map.get(model_type, lstm)
    explainer = shap.GradientExplainer(model, torch.from_numpy(closes))
    shap_values, indexes = explainer.shap_values(torch.from_numpy(closes)), None
    # أخذ الأهمية المطلقة لكل ميزة
    importances = np.abs(shap_values[0][0]).tolist()
    return jsonify({'importances': importances})

if __name__ == '__main__':
    app.run(port=5005, debug=True) 