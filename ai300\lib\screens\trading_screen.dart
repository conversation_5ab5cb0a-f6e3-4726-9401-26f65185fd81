import 'package:flutter/material.dart';
import '../trading_service.dart';
import 'dart:io';
import 'dart:convert';
import 'package:fl_chart/fl_chart.dart';

class TradingScreen extends StatefulWidget {
  const TradingScreen({super.key});
  @override
  State<TradingScreen> createState() => _TradingScreenState();
}

class _TradingScreenState extends State<TradingScreen> {
  final List<String> assets = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'];
  String selectedAsset = 'BTC/USDT';
  String side = 'buy';
  double amount = 0.001;
  bool testnet = true;
  String? resultMsg;
  String? balanceMsg;
  bool loading = false;
  String orderType = 'market'; // market, limit, stop
  double? price; // limit price
  double? stopPrice; // stop price
  List<Map<String, dynamic>> orderHistory = [];
  bool loadingOrders = false;
  String? filterStatus;
  String? filterType;
  double? sharpe, sortino, calmar, fixedFractional, kelly;
  double equity = 10000;
  double riskPerTrade = 0.02;
  double winRate = 0.55;
  double winLossRatio = 1.5;
  // Arbitrage UI state
  double arbitrageAmount = 0.01;
  double arbitrageMinProfit = 1;
  String arbitrageSymbol = 'BTC/USDT';
  String? arbitrageResultMsg;
  bool arbitrageLoading = false;
  // مراقبة التحكيم
  bool monitorArbitrage = false;
  List<String> arbitrageAlerts = [];
  double arbitrageMaxExposure = 0.05;
  int arbitrageInterval = 10;
  Stream<Map<String, dynamic>>? arbitrageMonitorStream;
  StreamSubscription? arbitrageMonitorSub;

  Future<void> executeOrder() async {
    setState(() { loading = true; resultMsg = null; });
    Map<String, dynamic> result;
    if (orderType == 'market') {
      result = await TradingService.placeOrder(
        symbol: selectedAsset,
        side: side,
        amount: amount,
        testnet: testnet,
      );
    } else if (orderType == 'limit') {
      result = await TradingService.placeLimitOrder(
        symbol: selectedAsset,
        side: side,
        amount: amount,
        price: price ?? 0,
        testnet: testnet,
      );
    } else {
      result = await TradingService.placeStopOrder(
        symbol: selectedAsset,
        side: side,
        amount: amount,
        stopPrice: stopPrice ?? 0,
        testnet: testnet,
      );
    }
    setState(() {
      loading = false;
      if (result['status'] == 'success') {
        resultMsg = 'تم تنفيذ الأمر بنجاح!\n${result['order']}';
        fetchOrderHistory();
      } else {
        resultMsg = 'خطأ: ${result['error']}';
      }
    });
    if (result['status'] == 'success') {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تنفيذ الأمر بنجاح!')));
    }
  }

  Future<void> fetchBalance() async {
    setState(() { balanceMsg = null; });
    final result = await TradingService.getBalance(testnet: testnet);
    setState(() {
      if (result['status'] == 'success') {
        balanceMsg = result['balance'].toString();
      } else {
        balanceMsg = 'خطأ: ${result['error']}';
      }
    });
  }

  Future<void> fetchOrderHistory() async {
    setState(() { loadingOrders = true; });
    final result = await TradingService.getOrderHistory(symbol: selectedAsset, testnet: testnet);
    setState(() {
      loadingOrders = false;
      if (result['status'] == 'success' && result['orders'] is List) {
        orderHistory = List<Map<String, dynamic>>.from(result['orders']);
      } else {
        orderHistory = [];
      }
    });
  }

  List<Map<String, dynamic>> get filteredOrderHistory {
    return orderHistory.where((o) {
      final statusMatch = filterStatus == null || filterStatus == 'الكل' || (o['status'] ?? '').toString() == filterStatus;
      final typeMatch = filterType == null || filterType == 'الكل' || (o['type'] ?? '').toString() == filterType;
      return statusMatch && typeMatch;
    }).toList();
  }

  Future<void> analyzeRisk() async {
    setState(() { loading = true; });
    final process = await Process.start('python', ['models/risk_management.py'], runInShell: true);
    await process.stdin.close();
    final output = await process.stdout.transform(utf8.decoder).join();
    sharpe = double.tryParse(RegExp(r'Sharpe: ([\d\.-]+)').firstMatch(output)?.group(1) ?? '');
    sortino = double.tryParse(RegExp(r'Sortino: ([\d\.-]+)').firstMatch(output)?.group(1) ?? '');
    calmar = double.tryParse(RegExp(r'Calmar: ([\d\.-]+)').firstMatch(output)?.group(1) ?? '');
    fixedFractional = equity * riskPerTrade;
    kelly = winRate - (1 - winRate) / winLossRatio;
    setState(() { loading = false; });
  }

  void showRiskSettingsDialog() {
    double tempEquity = equity;
    double tempRisk = riskPerTrade;
    double tempWinRate = winRate;
    double tempWinLoss = winLossRatio;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تخصيص إعدادات إدارة المخاطر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(children: [const Text('رأس المال:'), const SizedBox(width: 8), Expanded(child: TextField(controller: TextEditingController(text: tempEquity.toString()), keyboardType: TextInputType.number, onChanged: (v) => tempEquity = double.tryParse(v) ?? tempEquity)),]),
            Row(children: [const Text('نسبة المخاطرة:'), const SizedBox(width: 8), Expanded(child: TextField(controller: TextEditingController(text: (tempRisk*100).toString()), keyboardType: TextInputType.number, onChanged: (v) => tempRisk = (double.tryParse(v) ?? (tempRisk*100))/100)), const Text('%')]),
            Row(children: [const Text('نسبة الفوز:'), const SizedBox(width: 8), Expanded(child: TextField(controller: TextEditingController(text: (tempWinRate*100).toString()), keyboardType: TextInputType.number, onChanged: (v) => tempWinRate = (double.tryParse(v) ?? (tempWinRate*100))/100)), const Text('%')]),
            Row(children: [const Text('نسبة الربح/الخسارة:'), const SizedBox(width: 8), Expanded(child: TextField(controller: TextEditingController(text: tempWinLoss.toString()), keyboardType: TextInputType.number, onChanged: (v) => tempWinLoss = double.tryParse(v) ?? tempWinLoss)),]),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                equity = tempEquity;
                riskPerTrade = tempRisk;
                winRate = tempWinRate;
                winLossRatio = tempWinLoss;
              });
              analyzeRisk();
              Navigator.of(context).pop();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  Future<void> executeArbitrage() async {
    setState(() { arbitrageLoading = true; arbitrageResultMsg = null; });
    final result = await TradingService.executeArbitrage(
      symbol: arbitrageSymbol,
      amount: arbitrageAmount,
      minProfit: arbitrageMinProfit,
      testnet: testnet,
    );
    setState(() {
      arbitrageLoading = false;
      if (result['status'] == 'executed') {
        arbitrageResultMsg = 'تم تنفيذ التحكيم بنجاح!\n${result['opportunity']}';
      } else if (result['status'] == 'no_opportunity') {
        arbitrageResultMsg = 'لا توجد فرصة تحكيم حالياً.\nالأسعار: ${result['prices']}';
      } else {
        arbitrageResultMsg = 'خطأ: ${result['error']}';
      }
    });
  }

  void startArbitrageMonitor() {
    arbitrageAlerts.clear();
    arbitrageMonitorStream = TradingService.monitorArbitrage(
      symbol: arbitrageSymbol,
      amount: arbitrageAmount,
      minProfit: arbitrageMinProfit,
      maxExposure: arbitrageMaxExposure,
      testnet: testnet,
      interval: arbitrageInterval,
    );
    arbitrageMonitorSub = arbitrageMonitorStream!.listen((result) {
      if (result['status'] == 'executed') {
        setState(() {
          arbitrageAlerts.insert(0, 'فرصة تحكيم: ' + (result['alert'] ?? result['opportunity'].toString()));
        });
      }
    });
    setState(() { monitorArbitrage = true; });
  }

  void stopArbitrageMonitor() {
    arbitrageMonitorSub?.cancel();
    setState(() { monitorArbitrage = false; });
  }

  @override
  void initState() {
    super.initState();
    fetchOrderHistory();
    analyzeRisk();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('واجهة التداول')),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: loading
                ? const Center(child: CircularProgressIndicator())
                : Card(
                    color: Colors.grey[100],
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Text('مؤشرات إدارة المخاطر:', style: TextStyle(fontWeight: FontWeight.bold)),
                              const SizedBox(width: 8),
                              Tooltip(
                                message: 'Sharpe: يقيس العائد المعدل بالمخاطرة\nSortino: يقيس العائد مقابل المخاطرة السلبية\nCalmar: يقيس العائد مقابل أكبر تراجع',
                                child: const Icon(Icons.info_outline, size: 18),
                              ),
                              const Spacer(),
                              IconButton(
                                icon: const Icon(Icons.tune),
                                tooltip: 'تخصيص',
                                onPressed: showRiskSettingsDialog,
                              ),
                            ],
                          ),
                          Text('Sharpe Ratio: ${sharpe?.toStringAsFixed(3) ?? '-'}'),
                          Text('Sortino Ratio: ${sortino?.toStringAsFixed(3) ?? '-'}'),
                          Text('Calmar Ratio: ${calmar?.toStringAsFixed(3) ?? '-'}'),
                          Row(
                            children: [
                              Text('Fixed Fractional (2%): ${fixedFractional?.toStringAsFixed(2) ?? '-'}'),
                              const SizedBox(width: 8),
                              Tooltip(
                                message: 'Fixed Fractional: نسبة ثابتة من رأس المال',
                                child: const Icon(Icons.info_outline, size: 16),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Text('Kelly Criterion: ${(kelly != null && kelly! > 0) ? (kelly! * 100).toStringAsFixed(2) + '%' : '0%'}'),
                              const SizedBox(width: 8),
                              Tooltip(
                                message: 'Kelly: تعظيم النمو على المدى الطويل',
                                child: const Icon(Icons.info_outline, size: 16),
                              ),
                            ],
                          ),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: TextButton.icon(
                              icon: const Icon(Icons.refresh),
                              label: const Text('تحديث'),
                              onPressed: analyzeRisk,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text('الأصل:'),
                      const SizedBox(width: 8),
                      DropdownButton<String>(
                        value: selectedAsset,
                        items: assets.map((a) => DropdownMenuItem(value: a, child: Text(a))).toList(),
                        onChanged: (v) {
                          setState(() { selectedAsset = v!; });
                          fetchOrderHistory();
                        },
                      ),
                      const SizedBox(width: 16),
                      const Text('Testnet:'),
                      Switch(value: testnet, onChanged: (v) => setState(() { testnet = v; fetchOrderHistory(); })),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Text('نوع الأمر:'),
                      const SizedBox(width: 8),
                      DropdownButton<String>(
                        value: orderType,
                        items: const [
                          DropdownMenuItem(value: 'market', child: Text('Market')),
                          DropdownMenuItem(value: 'limit', child: Text('Limit')),
                          DropdownMenuItem(value: 'stop', child: Text('Stop')),
                        ],
                        onChanged: (v) => setState(() => orderType = v!),
                      ),
                      const SizedBox(width: 16),
                      const Text('الكمية:'),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 100,
                        child: TextField(
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                          decoration: const InputDecoration(hintText: 'مثال: 0.001'),
                          onChanged: (v) => amount = double.tryParse(v) ?? 0.001,
                        ),
                      ),
                      const SizedBox(width: 16),
                      ToggleButtons(
                        isSelected: [side == 'buy', side == 'sell'],
                        onPressed: (i) => setState(() => side = i == 0 ? 'buy' : 'sell'),
                        children: const [Text('شراء'), Text('بيع')],
                      ),
                    ],
                  ),
                  if (orderType == 'limit')
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Row(
                        children: [
                          const Text('Limit Price:'),
                          const SizedBox(width: 8),
                          SizedBox(
                            width: 100,
                            child: TextField(
                              keyboardType: TextInputType.numberWithOptions(decimal: true),
                              decoration: const InputDecoration(hintText: 'مثال: 30000'),
                              onChanged: (v) => price = double.tryParse(v),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (orderType == 'stop')
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Row(
                        children: [
                          const Text('Stop Price:'),
                          const SizedBox(width: 8),
                          SizedBox(
                            width: 100,
                            child: TextField(
                              keyboardType: TextInputType.numberWithOptions(decimal: true),
                              decoration: const InputDecoration(hintText: 'مثال: 29000'),
                              onChanged: (v) => stopPrice = double.tryParse(v),
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('تنفيذ الأمر'),
                    onPressed: loading ? null : executeOrder,
                  ),
                  if (resultMsg != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(resultMsg!, style: TextStyle(color: resultMsg!.startsWith('خطأ') ? Colors.red : Colors.green)),
                    ),
                  const Divider(),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.account_balance_wallet),
                    label: const Text('عرض الرصيد'),
                    onPressed: fetchBalance,
                  ),
                  if (balanceMsg != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(balanceMsg!, style: const TextStyle(fontFamily: 'monospace')),
                    ),
                  const Divider(),
                  Row(
                    children: [
                      const Text('سجل الأوامر', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: fetchOrderHistory,
                      ),
                      if (loadingOrders) const SizedBox(width: 8),
                      if (loadingOrders) const CircularProgressIndicator(strokeWidth: 2),
                      const Spacer(),
                      DropdownButton<String>(
                        value: filterStatus ?? 'الكل',
                        items: [const DropdownMenuItem(value: 'الكل', child: Text('كل الحالات'))] +
                          {
                            ...orderHistory.map((o) => o['status']?.toString() ?? '').where((s) => s.isNotEmpty)
                          }.map((s) => DropdownMenuItem(value: s, child: Text(s))).toList(),
                        onChanged: (v) => setState(() => filterStatus = v),
                      ),
                      const SizedBox(width: 8),
                      DropdownButton<String>(
                        value: filterType ?? 'الكل',
                        items: [const DropdownMenuItem(value: 'الكل', child: Text('كل الأنواع'))] +
                          {
                            ...orderHistory.map((o) => o['type']?.toString() ?? '').where((t) => t.isNotEmpty)
                          }.map((t) => DropdownMenuItem(value: t, child: Text(t))).toList(),
                        onChanged: (v) => setState(() => filterType = v),
                      ),
                    ],
                  ),
                  if (filteredOrderHistory.isNotEmpty)
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columns: const [
                          DataColumn(label: Text('ID')),
                          DataColumn(label: Text('النوع')),
                          DataColumn(label: Text('الحالة')),
                          DataColumn(label: Text('السعر')),
                          DataColumn(label: Text('الكمية')),
                          DataColumn(label: Text('الوقت')),
                        ],
                        rows: [
                          for (final o in filteredOrderHistory)
                            DataRow(cells: [
                              DataCell(Text(o['id'].toString())),
                              DataCell(Text(o['type'] ?? '-')),
                              DataCell(Text(o['status'] ?? '-')),
                              DataCell(Text(o['price']?.toString() ?? '-')),
                              DataCell(Text(o['amount']?.toString() ?? '-')),
                              DataCell(Text(o['datetime']?.toString() ?? '-')),
                            ]),
                        ],
                      ),
                    ),
                  if (!loadingOrders && filteredOrderHistory.isEmpty)
                    const Text('لا يوجد أوامر مطابقة.'),
                  const Divider(),
                  // --- Arbitrage Section ---
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('التحكيم متعدد المنصات', style: TextStyle(fontWeight: FontWeight.bold)),
                        Row(
                          children: [
                            const Text('الأصل:'),
                            const SizedBox(width: 8),
                            DropdownButton<String>(
                              value: arbitrageSymbol,
                              items: assets.map((a) => DropdownMenuItem(value: a, child: Text(a))).toList(),
                              onChanged: (v) => setState(() => arbitrageSymbol = v!),
                            ),
                            const SizedBox(width: 16),
                            const Text('الكمية:'),
                            const SizedBox(width: 8),
                            SizedBox(
                              width: 80,
                              child: TextField(
                                keyboardType: TextInputType.numberWithOptions(decimal: true),
                                decoration: const InputDecoration(hintText: 'مثال: 0.01'),
                                onChanged: (v) => arbitrageAmount = double.tryParse(v) ?? 0.01,
                              ),
                            ),
                            const SizedBox(width: 16),
                            const Text('الربح الأدنى:'),
                            const SizedBox(width: 8),
                            SizedBox(
                              width: 60,
                              child: TextField(
                                keyboardType: TextInputType.numberWithOptions(decimal: true),
                                decoration: const InputDecoration(hintText: '1'),
                                onChanged: (v) => arbitrageMinProfit = double.tryParse(v) ?? 1,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Text('Max Exposure:'),
                            const SizedBox(width: 8),
                            SizedBox(
                              width: 60,
                              child: TextField(
                                keyboardType: TextInputType.numberWithOptions(decimal: true),
                                decoration: const InputDecoration(hintText: '0.05'),
                                onChanged: (v) => arbitrageMaxExposure = double.tryParse(v) ?? 0.05,
                              ),
                            ),
                            const SizedBox(width: 16),
                            const Text('Interval (s):'),
                            const SizedBox(width: 8),
                            SizedBox(
                              width: 60,
                              child: TextField(
                                keyboardType: TextInputType.numberWithOptions(decimal: false),
                                decoration: const InputDecoration(hintText: '10'),
                                onChanged: (v) => arbitrageInterval = int.tryParse(v) ?? 10,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            ElevatedButton.icon(
                              icon: const Icon(Icons.compare_arrows),
                              label: const Text('تنفيذ التحكيم لمرة واحدة'),
                              onPressed: arbitrageLoading ? null : executeArbitrage,
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton.icon(
                              icon: Icon(monitorArbitrage ? Icons.stop : Icons.play_circle),
                              label: Text(monitorArbitrage ? 'إيقاف المراقبة' : 'بدء المراقبة'),
                              onPressed: monitorArbitrage ? stopArbitrageMonitor : startArbitrageMonitor,
                            ),
                          ],
                        ),
                        if (arbitrageLoading) const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: CircularProgressIndicator(),
                        ),
                        if (arbitrageResultMsg != null)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(arbitrageResultMsg!, style: TextStyle(color: arbitrageResultMsg!.startsWith('خطأ') ? Colors.red : Colors.green)),
                          ),
                        if (monitorArbitrage && arbitrageAlerts.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('تنبيهات التحكيم:', style: TextStyle(fontWeight: FontWeight.bold)),
                                ...arbitrageAlerts.take(10).map((a) => Text(a, style: const TextStyle(color: Colors.blue))),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 