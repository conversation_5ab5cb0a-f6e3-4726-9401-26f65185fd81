from flask import Flask, request, jsonify
from flask_cors import CORS
import numpy as np

app = Flask(__name__)
CORS(app)

def sharpe_ratio(returns, risk_free=0.0):
    returns = np.array(returns)
    excess = returns - risk_free
    return np.mean(excess) / (np.std(excess) + 1e-8)

def sortino_ratio(returns, risk_free=0.0):
    returns = np.array(returns)
    downside = returns[returns < risk_free]
    downside_std = np.std(downside) if len(downside) > 0 else 1e-8
    excess = returns - risk_free
    return np.mean(excess) / (downside_std + 1e-8)

def calmar_ratio(returns):
    returns = np.array(returns)
    max_drawdown = np.max(np.maximum.accumulate(returns) - returns)
    annual_return = np.mean(returns) * 252
    return annual_return / (max_drawdown + 1e-8)

def monte_carlo_sim(start, mu, sigma, steps, n_sim=1000):
    results = []
    for _ in range(n_sim):
        prices = [start]
        for _ in range(steps):
            prices.append(prices[-1] * np.exp((mu - 0.5 * sigma ** 2) + sigma * np.random.randn()))
        results.append(prices)
    return results

@app.route('/risk_metrics', methods=['POST'])
def risk_metrics():
    data = request.json
    returns = data['returns']
    risk_free = data.get('risk_free', 0.0)
    sharpe = sharpe_ratio(returns, risk_free)
    sortino = sortino_ratio(returns, risk_free)
    calmar = calmar_ratio(returns)
    return jsonify({'sharpe': sharpe, 'sortino': sortino, 'calmar': calmar})

@app.route('/monte_carlo', methods=['POST'])
def monte_carlo():
    data = request.json
    start = float(data['start'])
    mu = float(data['mu'])
    sigma = float(data['sigma'])
    steps = int(data['steps'])
    n_sim = int(data.get('n_sim', 1000))
    results = monte_carlo_sim(start, mu, sigma, steps, n_sim)
    return jsonify({'simulations': results})

if __name__ == '__main__':
    app.run(port=5008, debug=True) 