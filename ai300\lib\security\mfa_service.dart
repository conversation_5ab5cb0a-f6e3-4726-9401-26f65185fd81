import 'package:flutter_otp/flutter_otp.dart';

class MFAService {
  static final FlutterOtp _otp = FlutterOtp();
  static String? _lastCode;

  static Future<void> sendOtp(String phone) async {
    // إرسال رمز OTP عبر SMS
    _lastCode = _otp.generateOtp();
    // هنا يمكنك ربط خدمة SMS حقيقية
    print('OTP for $phone: $_lastCode');
  }

  static bool verifyOtp(String code) {
    return code == _lastCode;
  }
} 