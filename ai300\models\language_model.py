from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification

# يمكنك استبدال bert-base-uncased بنموذج LLaMA 3 أو أي نموذج مالي متخصص إذا توفر
MODEL_NAME = 'bert-base-uncased'
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForSequenceClassification.from_pretrained(MODEL_NAME)

nlp = pipeline('sentiment-analysis', model=model, tokenizer=tokenizer)

def analyze_text(text):
    result = nlp(text)
    return result[0]  # {'label': 'POSITIVE', 'score': 0.99}

if __name__ == '__main__':
    import sys
    text = sys.stdin.read()
    analysis = analyze_text(text)
    print(analysis) 