{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "integration_test", "path": "D:\\\\ai 300\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "android": [{"name": "integration_test", "path": "D:\\\\ai 300\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "macos": [], "linux": [], "windows": [], "web": []}, "dependencyGraph": [{"name": "integration_test", "dependencies": []}], "date_created": "2025-05-24 14:48:45.483819", "version": "3.32.0", "swift_package_manager_enabled": {"ios": false, "macos": false}}