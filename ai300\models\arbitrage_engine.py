import os
import ccxt
import time
import threading
from dotenv import load_dotenv
import smtplib
from email.mime.text import MIMEText
import requests as pyrequests

load_dotenv()

# API keys from environment variables (set these in your .env file)
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
BINANCE_API_SECRET = os.getenv('BINANCE_API_SECRET')
COINBASE_API_KEY = os.getenv('COINBASE_API_KEY')
COINBASE_API_SECRET = os.getenv('COINBASE_API_SECRET')
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_API_SECRET = os.getenv('KRAKEN_API_SECRET')
OANDA_API_KEY = os.getenv('OANDA_API_KEY')
OANDA_ACCOUNT_ID = os.getenv('OANDA_ACCOUNT_ID')

EXCHANGES = ['binance', 'coinbaseexchange', 'kraken']

# إعدادات التنبيه
ALERT_EMAIL = os.getenv('ALERT_EMAIL')
ALERT_EMAIL_PASSWORD = os.getenv('ALERT_EMAIL_PASSWORD')
ALERT_EMAIL_TO = os.getenv('ALERT_EMAIL_TO')
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# إدارة رأس المال
MAX_EXPOSURE = float(os.getenv('MAX_EXPOSURE', '0.05'))  # نسبة من الرصيد الكلي

# Create exchange clients
def create_exchange_clients():
    clients = {}
    clients['binance'] = ccxt.binance({
        'apiKey': BINANCE_API_KEY,
        'secret': BINANCE_API_SECRET,
        'enableRateLimit': True,
    })
    clients['coinbaseexchange'] = ccxt.coinbaseexchange({
        'apiKey': COINBASE_API_KEY,
        'secret': COINBASE_API_SECRET,
        'enableRateLimit': True,
    })
    clients['kraken'] = ccxt.kraken({
        'apiKey': KRAKEN_API_KEY,
        'secret': KRAKEN_API_SECRET,
        'enableRateLimit': True,
    })
    return clients

def fetch_prices(symbol='BTC/USDT'):
    clients = create_exchange_clients()
    prices = {}
    for name, client in clients.items():
        try:
            ticker = client.fetch_ticker(symbol)
            prices[name] = ticker['last']
        except Exception as e:
            prices[name] = None
    # OANDA (REST, only for FX)
    if symbol in ['EUR/USD', 'USD/JPY', 'GBP/USD'] and OANDA_API_KEY and OANDA_ACCOUNT_ID:
        import requests
        url = f'https://api-fxpractice.oanda.com/v3/accounts/{OANDA_ACCOUNT_ID}/pricing?instruments={symbol.replace("/", "_")}'
        headers = {'Authorization': f'Bearer {OANDA_API_KEY}'}
        r = requests.get(url, headers=headers)
        try:
            price = float(r.json()['prices'][0]['closeoutAsk'])
            prices['oanda'] = price
        except Exception:
            prices['oanda'] = None
    return prices

# إرسال تنبيه عبر البريد الإلكتروني
def send_email_alert(subject, body):
    if not ALERT_EMAIL or not ALERT_EMAIL_PASSWORD or not ALERT_EMAIL_TO:
        return False
    try:
        msg = MIMEText(body)
        msg['Subject'] = subject
        msg['From'] = ALERT_EMAIL
        msg['To'] = ALERT_EMAIL_TO
        with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
            server.login(ALERT_EMAIL, ALERT_EMAIL_PASSWORD)
            server.sendmail(ALERT_EMAIL, ALERT_EMAIL_TO, msg.as_string())
        return True
    except Exception as e:
        print('Email alert error:', e)
        return False

# إرسال تنبيه عبر التلغرام
def send_telegram_alert(message):
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHAT_ID:
        return False
    try:
        url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage'
        data = {'chat_id': TELEGRAM_CHAT_ID, 'text': message}
        r = pyrequests.post(url, data=data)
        return r.status_code == 200
    except Exception as e:
        print('Telegram alert error:', e)
        return False

# احتساب الرسوم
EXCHANGE_FEES = {
    'binance': 0.001,
    'coinbaseexchange': 0.0025,
    'kraken': 0.0026,
    'oanda': 0.0002
}

def fetch_balance(exchange_name, client):
    try:
        balance = client.fetch_balance()
        return balance['total'].get('USDT', 0) or balance['total'].get('USD', 0)
    except Exception:
        return 0

def find_arbitrage_opportunity(prices, min_profit=1, amount=0.01):
    valid = {k: v for k, v in prices.items() if v is not None}
    if len(valid) < 2:
        return None
    best = None
    for buy_ex in valid:
        for sell_ex in valid:
            if buy_ex == sell_ex:
                continue
            buy_price = valid[buy_ex]
            sell_price = valid[sell_ex]
            # احتساب الرسوم
            buy_fee = EXCHANGE_FEES.get(buy_ex, 0) * buy_price * amount
            sell_fee = EXCHANGE_FEES.get(sell_ex, 0) * sell_price * amount
            profit = (sell_price - buy_price) * amount - buy_fee - sell_fee
            if profit > min_profit:
                if not best or profit > best['profit']:
                    best = {
                        'buy_exchange': buy_ex,
                        'sell_exchange': sell_ex,
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'profit': profit,
                        'buy_fee': buy_fee,
                        'sell_fee': sell_fee
                    }
    return best

def execute_arbitrage(symbol, amount, min_profit=1, testnet=True, max_exposure=MAX_EXPOSURE):
    prices = fetch_prices(symbol)
    clients = create_exchange_clients()
    opp = find_arbitrage_opportunity(prices, min_profit, amount)
    if not opp:
        return {'status': 'no_opportunity', 'prices': prices}
    # إدارة رأس المال: تحقق من الرصيد
    buy_client = clients[opp['buy_exchange']]
    sell_client = clients[opp['sell_exchange']]
    buy_balance = fetch_balance(opp['buy_exchange'], buy_client)
    sell_balance = fetch_balance(opp['sell_exchange'], sell_client)
    # الحد الأقصى للصفقة
    max_amount = min(buy_balance, sell_balance) * max_exposure / opp['buy_price']
    exec_amount = min(amount, max_amount)
    if exec_amount <= 0:
        return {'status': 'insufficient_balance', 'opportunity': opp, 'buy_balance': buy_balance, 'sell_balance': sell_balance}
    try:
        if not testnet:
            buy_order = buy_client.create_market_buy_order(symbol, exec_amount)
            sell_order = sell_client.create_market_sell_order(symbol, exec_amount)
        else:
            buy_order = {'test': True, 'exchange': opp['buy_exchange'], 'side': 'buy', 'symbol': symbol, 'amount': exec_amount}
            sell_order = {'test': True, 'exchange': opp['sell_exchange'], 'side': 'sell', 'symbol': symbol, 'amount': exec_amount}
        # تنبيه
        alert_msg = f"Arbitrage Opportunity!\nBuy: {opp['buy_exchange']} @ {opp['buy_price']}\nSell: {opp['sell_exchange']} @ {opp['sell_price']}\nProfit: {opp['profit']:.2f}\nAmount: {exec_amount}\n"
        send_email_alert('Arbitrage Alert', alert_msg)
        send_telegram_alert(alert_msg)
        return {'status': 'executed', 'opportunity': opp, 'buy_order': buy_order, 'sell_order': sell_order, 'alert': alert_msg}
    except Exception as e:
        return {'status': 'error', 'error': str(e), 'opportunity': opp}

def arbitrage_monitor(symbol='BTC/USDT', amount=0.01, min_profit=1, interval=10, testnet=True, max_exposure=MAX_EXPOSURE):
    last_alert = None
    while True:
        result = execute_arbitrage(symbol, amount, min_profit, testnet, max_exposure)
        # فقط أرسل تنبيه إذا كانت هناك فرصة جديدة
        if result.get('status') == 'executed' and result.get('alert') != last_alert:
            print('تنبيه تحكيم:', result['alert'])
            last_alert = result['alert']
        else:
            print('لا توجد فرصة تحكيم حالياً.')
        time.sleep(interval)

if __name__ == '__main__':
    import argparse
    import json
    parser = argparse.ArgumentParser(description='Multi-Exchange Arbitrage Engine')
    parser.add_argument('--symbol', type=str, default='BTC/USDT')
    parser.add_argument('--amount', type=float, default=0.01)
    parser.add_argument('--min_profit', type=float, default=1)
    parser.add_argument('--testnet', action='store_true')
    parser.add_argument('--monitor', action='store_true')
    parser.add_argument('--interval', type=int, default=10)
    parser.add_argument('--max_exposure', type=float, default=MAX_EXPOSURE)
    args = parser.parse_args()
    if args.monitor:
        arbitrage_monitor(args.symbol, args.amount, args.min_profit, args.interval, args.testnet, args.max_exposure)
    else:
        result = execute_arbitrage(args.symbol, args.amount, args.min_profit, args.testnet, args.max_exposure)
        print(json.dumps(result, ensure_ascii=False, indent=2)) 