import '../data/models/candle_data.dart';

enum PatternType {
  bullish,
  bearish,
  neutral
}

class PatternResult {
  final String name;
  final PatternType type;
  final int startIndex;
  final int endIndex;
  final double reliability;
  
  PatternResult({
    required this.name,
    required this.type,
    required this.startIndex,
    required this.endIndex,
    required this.reliability,
  });
}

class PatternRecognition {
  // Candlestick Patterns
  static bool isDoji(CandleData candle) {
    final bodySize = (candle.close - candle.open).abs();
    final totalRange = candle.high - candle.low;
    return bodySize / totalRange < 0.1;
  }
  
  static bool isHammer(CandleData candle) {
    final bodySize = (candle.close - candle.open).abs();
    final upperShadow = candle.high - (candle.open > candle.close ? candle.open : candle.close);
    final lowerShadow = (candle.open < candle.close ? candle.open : candle.close) - candle.low;
    
    return bodySize / (candle.high - candle.low) < 0.3 && 
           upperShadow / bodySize < 0.5 && 
           lowerShadow / bodySize > 2.0;
  }
  
  static bool isEngulfing(List<CandleData> candles, int index) {
    if (index < 1 || index >= candles.length) return false;
    
    final current = candles[index];
    final previous = candles[index - 1];
    
    // Bullish engulfing
    if (previous.close < previous.open && // Previous is bearish
        current.close > current.open &&   // Current is bullish
        current.open < previous.close &&  // Current opens below previous close
        current.close > previous.open) {  // Current closes above previous open
      return true;
    }
    
    // Bearish engulfing
    if (previous.close > previous.open && // Previous is bullish
        current.close < current.open &&   // Current is bearish
        current.open > previous.close &&  // Current opens above previous close
        current.close < previous.open) {  // Current closes below previous open
      return true;
    }
    
    return false;
  }
  
  // Chart Patterns
  static PatternResult? findHeadAndShoulders(List<CandleData> candles, int startIndex, int lookback) {
    // Implementation of head and shoulders pattern recognition
    // This is a simplified version - real implementation would be more complex
    
    if (startIndex - lookback < 0) return null;
    
    // Find 5 key points for head and shoulders
    List<int> peaks = [];
    List<int> troughs = [];
    
    // Find local maxima and minima
    for (int i = startIndex - lookback + 2; i < startIndex - 1; i++) {
      if (candles[i].high > candles[i-1].high && 
          candles[i].high > candles[i+1].high) {
        peaks.add(i);
      }
      
      if (candles[i].low < candles[i-1].low && 
          candles[i].low < candles[i+1].low) {
        troughs.add(i);
      }
    }
    
    // Need at least 3 peaks and 2 troughs
    if (peaks.length < 3 || troughs.length < 2) return null;
    
    // Check if middle peak is higher than the other two
    if (candles[peaks[1]].high > candles[peaks[0]].high && 
        candles[peaks[1]].high > candles[peaks[2]].high) {
      // Check if the shoulders are roughly at the same level
      double shouldersDiff = (candles[peaks[0]].high - candles[peaks[2]].high).abs();
      double shouldersAvg = (candles[peaks[0]].high + candles[peaks[2]].high) / 2;
      
      if (shouldersDiff / shouldersAvg < 0.1) {
        return PatternResult(
          name: 'Head and Shoulders',
          type: PatternType.bearish,
          startIndex: peaks[0] - 2,
          endIndex: peaks[2] + 2,
          reliability: 0.7,
        );
      }
    }
    
    return null;
  }
}